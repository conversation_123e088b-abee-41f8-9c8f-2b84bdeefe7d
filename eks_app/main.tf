# resource "helm_release" "nginx-deployment" {
#   name       = "nginx-deployment"
#   repository = ".."
#   chart      = "test"
#   namespace  = "prometheus"
#   version   = "0.1.0"

#   values = [
#     file("../test/values/${terraform.workspace}.yaml")
#   ]
# }

# resource "helm_release" "playready_server" {
#   name       = "playready-server"
#   repository = "./charts/jon"
#   chart      = "playready"
#   namespace  = "test"
#   version    = "1.0.0"

#   values = [
#     file("./charts/jon/playready/values/${terraform.workspace}.yaml")
#   ]
# }
