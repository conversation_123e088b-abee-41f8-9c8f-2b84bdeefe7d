data "external" "jon_charts" {
  program = ["bash", "-c", "ls charts/jon | jq -R -s -c 'split(\"\n\")[:-1] | map({(.): \"1\"}) | add'"]
}

data "external" "coco_charts" {
  program = ["bash", "-c", "ls charts/coco | jq -R -s -c 'split(\"\n\")[:-1] | map({(.): \"1\"}) | add'"]
}

data "external" "luke_charts" {
  program = ["bash", "-c", "ls charts/luke | jq -R -s -c 'split(\"\n\")[:-1] | map({(.): \"1\"}) | add'"]
}