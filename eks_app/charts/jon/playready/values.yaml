image:
  repository: ************.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-jon-playready-server
#  tag: latest

replicaCount: 1

# Enabled HA
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 4
  targetCPUUtilizationPercentage: 60

resources:
  requests:
    cpu: '500m'
    memory: '1Gi'
  limits:
    cpu: '500m'
    memory: '1Gi'


pdb:
  maxUnavailable: 0

# priorityClassName: ''

roleArn: 'arn:aws:iam::************:role/saku-stage-jon-playready-server'

# https://gitlab.kkinternal.com/kkstream/jcl/jcl-infrastructure/tf-modules/terraform-aws-playready/-/blob/main/main.tf?ref_type=heads#L64
serviceAccount:
  create: true
  name: 'saku-stage-jon-playready-server'

# PlayReady Server Certificate is one of the important part which is used to start the service.
# AWS Secrets Manager will be used to store the server certificate.
# Fil the secret by <PERSON>
# https://gitlab.kkinternal.com/kkstream/jcl/jcl-infrastructure/tf-modules/terraform-aws-playready/-/blob/main/main.tf?ref_type=heads#L6
serverCertificateSecretName: '/app/saku/stage/jon/playready/server-certificate'
