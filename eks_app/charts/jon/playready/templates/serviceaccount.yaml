{{- if .Values.serviceAccount.create }}
{{- with .Values.serviceAccount }}
{{- $iamRoleAnnotations := dict "eks.amazonaws.com/role-arn" ($.Values.roleArn | default "") }}
{{- $annotations := merge $iamRoleAnnotations (.annotations | default dict ) }}
apiVersion: v1
kind: ServiceAccount
metadata:
  {{- if .name }}
  name: {{ .name | quote }}
  {{- else }}
  name: {{ $.Release.Name }}
  {{- end }}
  labels: {{- include "labels" $ | nindent 4 }}
  annotations: {{- toYaml $annotations | nindent 4 }}
automountServiceAccountToken: false
{{- end }}
{{- end }}