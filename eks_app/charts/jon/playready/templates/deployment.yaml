apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name | quote }}
  labels: {{- include "labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
  selector:
    matchLabels: {{- include "labels" . | nindent 6 }}
  template:
    metadata:
      labels: {{- include "labels" . | nindent 8 }}
    spec:
      {{- if .Values.serviceAccount.create }}
        {{- if .Values.serviceAccount.name }}
      serviceAccountName: {{ .Values.serviceAccount.name | quote }}
        {{- else }}
      serviceAccountName: {{ .Release.Name | quote }}
        {{- end }}
      {{- end }}
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
      {{- with .priorityClassName }}
      priorityClassName: {{ . | quote }}
      {{- end }}
      containers:
        - name: playready
          image: {{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          livenessProbe:
            initialDelaySeconds: 10
            httpGet:
              path: /healthz/live
              port: http
          readinessProbe:
            initialDelaySeconds: 30
            httpGet:
              path: '/healthz/ready'
              port: 'http'
          resources: {{- toYaml .Values.resources | nindent 12 }}
          env:
            - name: RMSDK__RevocationDataFile
              value: '/config/RevocationData.xml'
            - name: RMSDK__RobustnessVersionsDataFile
              value: '/config/RobustnessVersion.xml'
            - name: Playready__ServerCertificateSource__Type
              value: 'awssecretsmanager'
            - name: Playready__ServerCertificateSource__AwsSecretsManager__SecretName
              value: {{ .Values.serverCertificateSecretName | quote }}
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop: ['ALL']
            privileged: false
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 1001
          volumeMounts:
            - name: config
              mountPath: /config
              readOnly: true
            - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
              name: serviceaccount-token
              readOnly: true
      volumes:
        - name: config
          configMap:
            name: {{ .Release.Name | quote }}
            defaultMode: 0444
        - name: serviceaccount-token
          projected:
            sources:
              - serviceAccountToken:
                  expirationSeconds: 3607
                  path: token
              - configMap:
                  name: kube-root-ca.crt
                  items:
                    - key: ca.crt
                      path: ca.crt
              - downwardAPI:
                  items:
                    - path: namespace
                      fieldRef:
                        apiVersion: v1
                        fieldPath: metadata.namespace
            defaultMode: 0444
