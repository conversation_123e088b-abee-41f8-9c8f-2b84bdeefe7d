image:
  tag: v1.0.1

# PlayReady Server Certificate is one of the important part which is used to start the service.
# AWS Secrets Manager will be used to store the server certificate.
# Fil the secret by <PERSON>
# https://gitlab.kkinternal.com/kkstream/jcl/jcl-infrastructure/tf-modules/terraform-aws-playready/-/blob/main/main.tf?ref_type=heads#L6
serverCertificateSecretName: "/app/saku/prep/jon/playready/server-certificate"

serviceAccount:
  name: "saku-prep-jon-playready-server"

roleArn: "arn:aws:iam::************:role/saku-prep-jon-playready-server"
