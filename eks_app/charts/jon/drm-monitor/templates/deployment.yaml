{{- if .Values.enabled }}
{{- $fullname := include "jon-drm-monitor.fullname" . -}}

{{- if .Values.fairplay.ENABLE_FAIRPLAY }}
{{ $deployName := printf "%s-%s" $fullname "fairplay" }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $deployName }}
  labels:
    {{- include "jon-drm-monitor.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  strategy:
    type: {{ .Values.updateStrategy.type }}
    {{- if eq .Values.updateStrategy.type "RollingUpdate" }}
    rollingUpdate:
      maxSurge: {{ .Values.updateStrategy.rollingUpdate.maxSurge }}
      maxUnavailable: {{ .Values.updateStrategy.rollingUpdate.maxUnavailable }}
    {{- end }}
  selector:
    matchLabels:
      {{- include "jon-drm-monitor.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "jon-drm-monitor.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "jon-drm-monitor.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command:
            {{- toYaml .Values.command | nindent 12 }}
          workingDir: /kkstream/drm-workdir
          ports:
            - name: http
              containerPort: {{ .Values.container.port }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: {{ .Values.container.liveness.path }}
              port: {{ .Values.container.liveness.port }}
              scheme: HTTP
            initialDelaySeconds: {{ .Values.container.livenessInitialDelaySeconds }}
            periodSeconds: {{ .Values.container.livenessPeriodSeconds }}
          readinessProbe:
            httpGet:
              path: {{ .Values.container.readiness.path }}
              port: {{ .Values.container.readiness.port }}
              scheme: HTTP
            initialDelaySeconds: {{ .Values.container.readinessInitialDelaySeconds }}
            periodSeconds: {{ .Values.container.readinessPeriodSeconds }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          envFrom:
            - configMapRef:
                name: {{ $fullname }}-env
            - configMapRef:
                name: {{ $deployName }}-env
          volumeMounts:
            {{- toYaml .volumeMounts | nindent 12 }}
          terminationMessagePath: {{ .Values.terminationMessage.path }}
          terminationMessagePolicy: {{ .Values.terminationMessage.policy }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
{{- end }}

{{- if .Values.playready.ENABLE_PLAYREADY }}
{{ $deployName := printf "%s-%s" $fullname "playready" }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $deployName }}
  labels:
    {{- include "jon-drm-monitor.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  strategy:
    type: {{ .Values.updateStrategy.type }}
    {{- if eq .Values.updateStrategy.type "RollingUpdate" }}
    rollingUpdate:
      maxSurge: {{ .Values.updateStrategy.rollingUpdate.maxSurge }}
      maxUnavailable: {{ .Values.updateStrategy.rollingUpdate.maxUnavailable }}
    {{- end }}
  selector:
    matchLabels:
      {{- include "jon-drm-monitor.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "jon-drm-monitor.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "jon-drm-monitor.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command:
            {{- toYaml .Values.command | nindent 12 }}
          workingDir: /kkstream/drm-workdir
          ports:
            - name: http
              containerPort: {{ .Values.container.port }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: {{ .Values.container.liveness.path }}
              port: {{ .Values.container.liveness.port }}
              scheme: HTTP
            initialDelaySeconds: {{ .Values.container.livenessInitialDelaySeconds }}
            periodSeconds: {{ .Values.container.livenessPeriodSeconds }}
          readinessProbe:
            httpGet:
              path: {{ .Values.container.readiness.path }}
              port: {{ .Values.container.readiness.port }}
              scheme: HTTP
            initialDelaySeconds: {{ .Values.container.readinessInitialDelaySeconds }}
            periodSeconds: {{ .Values.container.readinessPeriodSeconds }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          envFrom:
            - configMapRef:
                name: {{ $fullname }}-env
            - configMapRef:
                name: {{ $deployName }}-env
          volumeMounts:
            {{- toYaml .volumeMounts | nindent 12 }}
          terminationMessagePath: {{ .Values.terminationMessage.path }}
          terminationMessagePolicy: {{ .Values.terminationMessage.policy }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}

{{- if .Values.widevine.ENABLE_WIDEVINE }}
{{ $deployName := printf "%s-%s" $fullname "widevine" }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $deployName }}
  labels:
    {{- include "jon-drm-monitor.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  strategy:
    type: {{ .Values.updateStrategy.type }}
    {{- if eq .Values.updateStrategy.type "RollingUpdate" }}
    rollingUpdate:
      maxSurge: {{ .Values.updateStrategy.rollingUpdate.maxSurge }}
      maxUnavailable: {{ .Values.updateStrategy.rollingUpdate.maxUnavailable }}
    {{- end }}
  selector:
    matchLabels:
      {{- include "jon-drm-monitor.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "jon-drm-monitor.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "jon-drm-monitor.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command:
            {{- toYaml .Values.command | nindent 12 }}
          workingDir: /kkstream/drm-workdir
          ports:
            - name: http
              containerPort: {{ .Values.container.port }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: {{ .Values.container.liveness.path }}
              port: {{ .Values.container.liveness.port }}
              scheme: HTTPS
            initialDelaySeconds: {{ .Values.container.livenessInitialDelaySeconds }}
            periodSeconds: {{ .Values.container.livenessPeriodSeconds }}
          readinessProbe:
            httpGet:
              path: {{ .Values.container.readiness.path }}
              port: {{ .Values.container.readiness.port }}
              scheme: HTTPS
            initialDelaySeconds: {{ .Values.container.readinessInitialDelaySeconds }}
            periodSeconds: {{ .Values.container.readinessPeriodSeconds }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          envFrom:
            - configMapRef:
                name: {{ $fullname }}-env
            - configMapRef:
                name: {{ $deployName }}-env
          volumeMounts:
            {{- toYaml .volumeMounts | nindent 12 }}
          terminationMessagePath: {{ .Values.terminationMessage.path }}
          terminationMessagePolicy: {{ .Values.terminationMessage.policy }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}

{{- end }}