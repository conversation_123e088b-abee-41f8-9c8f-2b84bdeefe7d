{{- if .Values.enabled }}
{{- $fullname := include "jon-drm-monitor.fullname" . -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullname }}-env
  labels:
    {{- include "jon-drm-monitor.labels" . | nindent 4 }}
data:
  {{- range $key, $val := .Values.configMapEnv }}
    {{- if $val }}
      {{ $key }}: {{ $val | quote }}
    {{- else }}
      {{ $key }}: ""
    {{- end }}
  {{- end }}
{{- if .Values.fairplay.ENABLE_FAIRPLAY }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullname }}-fairplay-env
  labels:
    {{- include "jon-drm-monitor.labels" . | nindent 4 }}
data:
  {{- range $key, $val := .Values.fairplay }}
    {{- if $val}}
      {{ $key }}: {{ $val | quote }}
    {{- else }}
      {{ $key }}: ""
    {{- end }}
  {{- end }}
{{- end }}
{{- if .Values.playready.ENABLE_PLAYREADY }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullname }}-playready-env
  labels:
    {{- include "jon-drm-monitor.labels" . | nindent 4 }}
data:
  {{- range $key, $val := .Values.playready }}
    {{- if $val}}
      {{ $key }}: {{ $val | quote }}
    {{- else }}
      {{ $key }}: ""
    {{- end }}
  {{- end }}
{{- end }}
{{- if .Values.widevine.ENABLE_WIDEVINE }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullname }}-widevine-env
  labels:
    {{- include "jon-drm-monitor.labels" . | nindent 4 }}
data:
  {{- range $key, $val := .Values.widevine }}
    {{- if $val}}
      {{ $key }}: {{ $val | quote }}
    {{- else }}
      {{ $key }}: ""
    {{- end }}
  {{- end }}
{{- end }}

{{- end }}