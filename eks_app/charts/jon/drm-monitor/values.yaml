# Default values for jcl-jon-drm-monitor-helm.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true

fairplayEnabled: true
playreadyEnabled: true
widevineEnabled: true

namespace: jcl-jon

replicaCount: 1

image:
  repository: 138046196805.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-jon-monitor
  tag: ""
  pullPolicy: IfNotPresent

strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 1

nameOverride: "jon-drm-monitor"
fullnameOverride: ""

service: {}

resources: 
  limits:
   cpu: 150m
   memory: 150Mi
  requests:
   cpu: 100m
   memory: 120Mi

nodeSelector: {}
  # purpose: nodes

podAnnotations: {}

tolerations: []

affinity: {}
  # nodeAffinity:
  #   requiredDuringSchedulingIgnoredDuringExecution:
  #     nodeSelectorTerms:
  #     - matchExpressions:
  #       - key: purpose
  #         operator: In
  #         values:
  #         - nodes
  # podAntiAffinity:
  #   preferredDuringSchedulingIgnoredDuringExecution:
  #   - weight: 100
  #     podAffinityTerm:
  #       labelSelector:
  #         matchExpressions:
  #         - key: app.kubernetes.io/name
  #           operator: In
  #           values:
  #           - jon-drm-monitor
  #       topologyKey: kubernetes.io/hostname

configMap:
  # Enabled=true is overwrite configmap data
  enabled: False
  defaultData: {}
  fairplayData: {}
  playreadyData: {}
  widevineData: {}

fairplay:
  enable: false

playready:
  enable: false

widevine:
  enable: false

topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: topology.kubernetes.io/zone
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app.kubernetes.io/instance:  "{{ .Release.Name }}"
  - maxSkew: 1
    topologyKey: kubernetes.io/hostname
    whenUnsatisfiable: DoNotSchedule
    labelSelector:
      matchLabels:
        app.kubernetes.io/instance: "{{ .Release.Name }}"
