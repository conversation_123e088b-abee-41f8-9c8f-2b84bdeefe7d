# Default values for sample.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true
enableTest: false
replicaCount: 1

updateStrategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 25%
    maxUnavailable: 25%

image:
  repository: ************.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-jon-monitor
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "1.19.1105"

volumeMounts: []

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: false

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 150m
    memory: 150Mi
  requests:
    cpu: 100m
    memory: 120Mi

command:
  - node
  - monitor.js

container:
  port: 80
  liveness:
    path: /healthz
    port: 80
  livenessInitialDelaySeconds: 60
  livenessPeriodSeconds: 60
  readiness:
    path: /readiness
    port: 80
  readinessInitialDelaySeconds: 10
  readinessPeriodSeconds: 30

autoscaling:
  enabled: false

vpa:
  enabled: false

nodeSelector: {}

tolerations: []

affinity: {}

terminationMessage:
  path: /dev/termination-log
  policy: File

configMapEnv:
  LISTEN_PORT: "80"
  LICENSE_EXPIRE_TIME: "86400"
  CONNECTION_CHECK_INTERVAL: "60"
  TIMEOUT_LIMIT: "3"

fairplay:
  enable: true
  ENABLE_FAIRPLAY: "True"
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  FAIRPLAY_SERVER_CERTIFICATE: "MIIE+DCCA+CgAwIBAgIIF3MlC3HvFZgwDQYJKoZIhvcNAQEFBQAwfzELMAkGA1UEBhMCVVMxEzARBgNVBAoMCkFwcGxlIEluYy4xJjAkBgNVBAsMHUFwcGxlIENlcnRpZmljYXRpb24gQXV0aG9yaXR5MTMwMQYDVQQDDCpBcHBsZSBLZXkgU2VydmljZXMgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkwHhcNMTkwNTI4MDk0MDU0WhcNMjEwNTI4MDk0MDU0WjCBlDELMAkGA1UEBhMCSlAxLTArBgNVBAoMJEp1cGl0ZXIgVGVsZWNvbW11bmljYXRpb25zIENvLiwgTHRkLjETMBEGA1UECwwKRktVREY2SFEyTjFBMD8GA1UEAww4RmFpclBsYXkgU3RyZWFtaW5nOiBKdXBpdGVyIFRlbGVjb21tdW5pY2F0aW9ucyBDby4sIEx0ZC4wgZ8wDQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBAKE1Q6uKIUTHoAs0U7UZ8wCKUmtr3IRvUyzcaDYBxwAAVI0QbZEX04wg5X1mIEUtsdEN487LEjr7A1muns+UHGl8MxtgBdOm/HXLpKSgdmS+3rsLV/EONK6XtW246IRvlrY+Fg+faMUP+Xg1tmzPtZY/Kw6vpChj8sL9QXW2QHdXAgMBAAGjggHkMIIB4DAMBgNVHRMBAf8EAjAAMB8GA1UdIwQYMBaAFGPkR1TLhXFZRiyDrMxEMWRnAyy+MIHiBgNVHSAEgdowgdcwgdQGCSqGSIb3Y2QFATCBxjCBwwYIKwYBBQUHAgIwgbYMgbNSZWxpYW5jZSBvbiB0aGlzIGNlcnRpZmljYXRlIGJ5IGFueSBwYXJ0eSBhc3N1bWVzIGFjY2VwdGFuY2Ugb2YgdGhlIHRoZW4gYXBwbGljYWJsZSBzdGFuZGFyZCB0ZXJtcyBhbmQgY29uZGl0aW9ucyBvZiB1c2UsIGNlcnRpZmljYXRlIHBvbGljeSBhbmQgY2VydGlmaWNhdGlvbiBwcmFjdGljZSBzdGF0ZW1lbnRzLjA1BgNVHR8ELjAsMCqgKKAmhiRodHRwOi8vY3JsLmFwcGxlLmNvbS9rZXlzZXJ2aWNlcy5jcmwwHQYDVR0OBBYEFG9AtKiPuvzFrxL2yiOzsJpjU/oRMA4GA1UdDwEB/wQEAwIFIDApBgsqhkiG92NkBg0BAwEB/wQXATA3Z2xnOHdxNW02YnJzbWltOGlteGowOQYLKoZIhvdjZAYNAQQBAf8EJwFqM2hiemlmN3pucnI4ZHkzdXF4ZmdlYmE5YmhzeGFqcnlob255YTANBgkqhkiG9w0BAQUFAAOCAQEAShtn7ILu35v/VuXYdjq+fap9tWpJs9K8SfA5uJuTei9Nt5pRj5pSyvqGTt5fqTkneb7SzK0kdhadYOcWZtORpIPeFXfO4AGTAAScuTasioMGHjfIkLu/RmTUFLNV6KNPFRi4BVkTl/LuNivh+Fy8U8MdoUnrV/GW8DJXZUccmF8aQ/tFivujboxw4R4+jslPLZjZGvae+ltALD5RvN6yd+Z79ufFA0fXo3iQxCkZrPeV+iADLMtzJlZ640znrQlXTpGeZ1lsnlvfHfiEXRmpeNjgaB0apzq+nkEPpmCkOrT6nS7GQBqAYbRdTnMwPCSq2smvMQ1r7CdJdPhD/9bWiw=="
  FAIRPLAY_ASK: "vdt6nLfmhmo8gscrl7PT8Q=="

playready:
  enable: true
  ENABLE_PLAYREADY: "True"
  PLAYREADY_SERVER_URL: "http://saku-stag-playready.kkstream.tech/playready/RightsManager.asmx"
  PLAYREADY_SERVICE_ID: "UxbOJRBwskmrA5a0bbvNqA=="

widevine:
  enable: true
  ENABLE_WIDEVINE: "True"
  WIDEVINE_LICENSE_SERVER: "https://license.widevine.com/cenc/getlicense/​kkboxjcom"
  WIDEVINE_PROVIDER: "name:kkboxjcom,key:7cbc92b31c3e12a8f7035add51b0c47d4de4f80c756e5f52801da788e2282511,iv:f28e5636c78fe9aff6e9051cf7bc092c"
  WIDEVINE_CONTENT_KEY_SPEC: "hdcp:HDCP_NONE,cgms:CGMS_NONE,secure_data_path:false"
  WIDEVINE_POLICY: "playback_duration_seconds:0,can_play:true,can_persist:false"
