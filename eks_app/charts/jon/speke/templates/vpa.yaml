{{- if and .Values.enabled .Values.vpa.enabled }}
{{- $fullName := include "jon-speke.fullname" . -}}
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "jon-speke.labels" . | nindent 4 }}
spec:
  resourcePolicy:
    containerPolicies:
      - containerName: '*'
        controlledResources:
          - cpu
          - memory
        {{- toYaml .Values.vpa.resources | nindent 8 }}
        mode: Auto
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $fullName }}
  updatePolicy:
    minReplicas: 1
    updateMode: Auto
{{- end }}
