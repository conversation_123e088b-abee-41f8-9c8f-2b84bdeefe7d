{{- if .Values.enableTest }}
apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "jon-speke.fullname" . }}-test-connection"
  labels:
    {{- include "jon-speke.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "jon-speke.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
{{- end }}
