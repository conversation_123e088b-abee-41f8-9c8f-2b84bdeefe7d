{{- if .Values.enabled }}
{{- $fullname := include "jon-speke.fullname" . -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "jon-speke.fullname" . }}
  labels:
    {{- include "jon-speke.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  strategy:
    type: {{ .Values.updateStrategy.type }}
    {{- if eq .Values.updateStrategy.type "RollingUpdate" }}
    rollingUpdate:
      maxSurge: {{ .Values.updateStrategy.rollingUpdate.maxSurge }}
      maxUnavailable: {{ .Values.updateStrategy.rollingUpdate.maxUnavailable }}
    {{- end }}
  selector:
    matchLabels:
      {{- include "jon-speke.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        {{- if .Values.prometheus_metrics.enabled }}
        prometheus.io/scrape: "{{ .Values.prometheus_metrics.enabled }}"
        prometheus.io/port: "{{ .Values.prometheus_metrics.port }}"
        {{- end }}
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
      labels:
        {{- include "jon-speke.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "jon-speke.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      initContainers:
        - name: nginx-tuning
          image: docker.io/busybox
          command:
            - sh
            - "-c"
          args:
            - >
              sysctl -w fs.file-max=2097152 kernel.sysrq=0
              kernel.core_uses_pid=1 kernel.msgmnb=65536 kernel.msgmax=65536
              kernel.shmmax=*********** kernel.shmall=**********

              sysctl -w vm.swappiness=10 vm.dirty_ratio=40
              vm.dirty_background_ratio=10 net.core.somaxconn=65535

              sysctl -w net.ipv4.conf.default.rp_filter=1
              net.ipv4.conf.default.accept_source_route=0 net.ipv4.ip_forward=0
              net.ipv4.ip_local_port_range="2000 65535"

              ulimit -l unlimited

              sleep 5
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          securityContext:
            privileged: true
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command:
            {{- toYaml .Values.command | nindent 12 }}
          ports:
            - name: http
              containerPort: {{ .Values.container.port }}
              protocol: TCP
          livenessProbe:
            httpGet:
              {{- toYaml .Values.container.liveness | nindent 14}}
            initialDelaySeconds: {{ .Values.container.livenessInitialDelaySeconds }}
            periodSeconds: {{ .Values.container.livenessPeriodSeconds }}
          readinessProbe:
            httpGet:
              {{- toYaml .Values.container.readiness | nindent 14}}
            initialDelaySeconds: {{ .Values.container.readinessInitialDelaySeconds }}
            periodSeconds: {{ .Values.container.readinessPeriodSeconds }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          envFrom:
            - configMapRef:
                name: {{ $fullname }}-env
          volumeMounts:
            {{- toYaml .volumeMounts | nindent 12 }}
          terminationMessagePath: {{ .Values.terminationMessage.path }}
          terminationMessagePolicy: {{ .Values.terminationMessage.policy }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
{{- end }}
