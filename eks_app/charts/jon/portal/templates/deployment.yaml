{{- if .Values.enabled }}
{{- $fullname := include "jon-portal.fullname" . -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "jon-portal.fullname" . }}
  labels:
    {{- include "jon-portal.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  strategy:
    type: {{ .Values.updateStrategy.type }}
    {{- if eq .Values.updateStrategy.type "RollingUpdate" }}
    rollingUpdate:
      maxSurge: {{ .Values.updateStrategy.rollingUpdate.maxSurge }}
      maxUnavailable: {{ .Values.updateStrategy.rollingUpdate.maxUnavailable }}
    {{- end }}
  selector:
    matchLabels:
      {{- include "jon-portal.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        {{- if .Values.prometheus_metrics.enabled }}
        prometheus.io/scrape: "{{ .Values.prometheus_metrics.enabled }}"
        prometheus.io/port: "{{ .Values.prometheus_metrics.port }}"
        {{- end }}
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
      labels:
        {{- include "jon-portal.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "jon-portal.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      volumes:
        - name: drmlog-debug
          emptyDir: {}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command:
            {{- toYaml .Values.command | nindent 12 }}
          workingDir: /kkstream/drm-workdir
          ports:
            - name: http
              containerPort: {{ .Values.container.port }}
              protocol: TCP
          livenessProbe:
            httpGet:
              {{- toYaml .Values.container.liveness | nindent 14}}
            initialDelaySeconds: {{ .Values.container.livenessInitialDelaySeconds }}
            periodSeconds: {{ .Values.container.livenessPeriodSeconds }}
          readinessProbe:
            httpGet:
              {{- toYaml .Values.container.readiness | nindent 14}}
            initialDelaySeconds: {{ .Values.container.readinessInitialDelaySeconds }}
            periodSeconds: {{ .Values.container.readinessPeriodSeconds }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          envFrom:
            - configMapRef:
                name: {{ $fullname }}-env
            - secretRef:
                name: {{ $fullname }}-env
          volumeMounts:
            {{- toYaml .volumeMounts | nindent 12 }}
          terminationMessagePath: {{ .Values.terminationMessage.path }}
          terminationMessagePolicy: {{ .Values.terminationMessage.policy }}
        # - name: fluentd-s3
        #   image: 138046196805.dkr.ecr.ap-northeast-1.amazonaws.com/kubernetes-fluentd-s3:20181015_2
        #   env:
        #     - name: S3_LOGS_BUCKET_NAME
        #       valueFrom:
        #         configMapKeyRef:
        #           name: {{ $fullname }}-env
        #           key: S3_LOGS_BUCKET_NAME
        #     - name: S3_LOGS_BUCKET_PREFIX
        #       valueFrom:
        #         configMapKeyRef:
        #           name: {{ $fullname }}-env
        #           key: S3_LOGS_BUCKET_PREFIX
        #     - name: S3_LOGS_BUCKET_REGION
        #       valueFrom:
        #         configMapKeyRef:
        #           name: {{ $fullname }}-env
        #           key: S3_LOGS_BUCKET_REGION
        #     - name: K8S_APP_TAG
        #       valueFrom:
        #         configMapKeyRef:
        #           name: {{ $fullname }}-env
        #           key: K8S_APP_TAG
        #   resources:
        #     limits:
        #       cpu: 150m
        #       memory: 250Mi
        #     requests:
        #       cpu: 100m
        #       memory: 200Mi
        #   volumeMounts:
        #     - name: drmlog-debug
        #       mountPath: /var/log/kkstream
        #   terminationMessagePath: /dev/termination-log
        #   terminationMessagePolicy: File
        #   imagePullPolicy: IfNotPresent
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
{{- end }}
