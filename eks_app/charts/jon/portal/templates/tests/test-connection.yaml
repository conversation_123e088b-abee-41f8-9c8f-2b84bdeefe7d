{{- if .Values.enableTest }}
apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "jon-portal.fullname" . }}-test-connection"
  labels:
    {{- include "jon-portal.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "jon-portal.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
{{- end }}
