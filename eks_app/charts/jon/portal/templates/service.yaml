{{- if .Values.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "jon-portal.fullname" . }}
  labels:
    {{- include "jon-portal.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "jon-portal.selectorLabels" . | nindent 4 }}
{{- end }}
