{{- if .Values.enabled }}
{{- $fullname := include "jon-portal.fullname" . -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullname }}-env
  labels:
    {{- include "jon-portal.labels" . | nindent 4 }}
data:
  {{- range $key, $val := .Values.configMapEnv }}
    {{- if $val }}
      {{ $key }}: {{ tpl $val $ | quote }}
    {{- else }}
      {{ $key }}: ""
    {{- end }}
  {{- end }}
{{- end }}
