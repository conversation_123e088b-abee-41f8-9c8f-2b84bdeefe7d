# Default values for sample.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true
enableTest: false
replicaCount: 1

updateStrategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 25%
    maxUnavailable: 25%

image:
  repository: ************.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-jon-drm-portal
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

volumeMounts:
  - name: drmlog-debug
    mountPath: /var/log/kkstream

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  className: "alb"
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /healthz
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
    alb.ingress.kubernetes.io/scheme: "internet-facing"
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/ssl-policy: "ELBSecurityPolicy-TLS13-1-2-2021-06"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: Prefix
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 800m
    memory: 1000Mi
  requests:
    cpu: 500m
    memory: 800Mi

command:
  - node
  - drm-server.js

container:
  port: 8080
  liveness:
    path: /healthz
    port: 8080
    scheme: HTTP
  livenessInitialDelaySeconds: 0
  livenessPeriodSeconds: 10
  readiness:
    path: /readiness
    port: 8080
    scheme: HTTP
  readinessInitialDelaySeconds: 0
  readinessPeriodSeconds: 10

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 50
  targetMemoryUtilizationPercentage: 50

vpa:
  enabled: true
  resources:
    maxAllowed:
      cpu: 800m
      memory: 800Mi
    minAllowed:
      cpu: 100m
      memory: 100Mi

nodeSelector: {}

tolerations: []

affinity: {}

terminationMessage:
  path: /dev/termination-log
  policy: File

prometheus_metrics:
  enabled: false

configMapEnv:

secretEnv:

topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: topology.kubernetes.io/zone
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app.kubernetes.io/instance:  "{{ .Release.Name }}"
  - maxSkew: 1
    topologyKey: kubernetes.io/hostname
    whenUnsatisfiable: DoNotSchedule
    labelSelector:
      matchLabels:
        app.kubernetes.io/name: "{{ .Release.Name }}"

pdb:
  enabled: true
  maxUnavailable: 0