# Default values for sample.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true
enableTest: false
replicaCount: 1

updateStrategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 25%
    maxUnavailable: 25%

image:
  repository: ************.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-jon-drm-portal
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "1.23.1201"

volumeMounts:
  - name: drmlog-debug
    mountPath: /var/log/kkstream

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  className: "alb"
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /healthz
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/ssl-redirect: "443"
    alb.ingress.kubernetes.io/scheme: "internet-facing"
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/subnets: "saku-stag-public-1c,saku-stag-public-1a"
    alb.ingress.kubernetes.io/group.name: "saku-prep-jon"
    alb.ingress.kubernetes.io/group.order: "3"
    alb.ingress.kubernetes.io/ssl-policy: "ELBSecurityPolicy-TLS13-1-2-2021-06"
    external-dns.alpha.kubernetes.io/aws-weight: "100"
    external-dns.alpha.kubernetes.io/set-identifier: "saku-prep-eks"
  hosts:
    - host: "pre-jon-saku.kkstream.tech"
      paths:
        - path: /
          pathType: Prefix

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 800m
    memory: 1000Mi
  requests:
    cpu: 500m
    memory: 800Mi

command:
  - node
  - drm-server.js

container:
  port: 8080
  liveness:
    path: /healthz
    port: 8080
    scheme: HTTP
  livenessInitialDelaySeconds: 0
  livenessPeriodSeconds: 10
  readiness:
    path: /readiness
    port: 8080
    scheme: HTTP
  readinessInitialDelaySeconds: 0
  readinessPeriodSeconds: 10

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 50
  targetMemoryUtilizationPercentage: 50

vpa:
  enabled: false
  resources:
    maxAllowed:
      cpu: 800m
      memory: 800Mi
    minAllowed:
      cpu: 100m
      memory: 100Mi

nodeSelector: {}

tolerations: []

affinity: {}

terminationMessage:
  path: /dev/termination-log
  policy: File

prometheus_metrics:
  enabled: true
  port: 8080

configMapEnv:
  DRM_AUTH_SERVER_DOWNLOAD: "https://api.saku.preottfs.com/download"
  DRM_AUTH_SERVER_PLAYBACK: "https://api.saku.preottfs.com/playback"
  DRM_DEFAULT_EXPIRE_TIME: "86400"
  DRM_ENABLE_FAIRPLAY: "True"
  DRM_ENABLE_HLS_CLEARKEY: "False"
  DRM_ENABLE_LOGGER: "True"
  DRM_ENABLE_PLAYREADY: "True"
  DRM_ENABLE_WIDEVINE: "True"
  DRM_NULL_TOKEN_ENABLE: "False"
  DRM_NULL_TOKEN_VALUE: "********************************,f4f24f6976825a1cdd3540db3b77df1e,df2ffc6e3eb5b561fc87a080b45c5fc1,52d042fa8ed41f7f3d1f5721cc192022,2c8b96505815c6bdba5c555c4bb46908,987809cc666f0e4d89c5fdfcdb22fe2a"
  DRM_PLAYREADY_IS_BLOCKING_ANALOG_OUTPUT: "True"
  DRM_PLAYREADY_IS_MIN_SECURITY_LEVEL_150: "False"
  DRM_PLAYREADY_OPL_SETTING: "AnalogVideoOPL:200,CompressedDigitalAudioOPL:300,CompressedDigitalVideoOPL:500,UncompressedDigitalAudioOPL:300,UncompressedDigitalVideoOPL:300"
  DRM_PLAYREADY_SERVER_URL: "http://prep-jon-playready.prep-jon/playready/RightsManager.asmx"
  DRM_VENDOR_TOKEN_ENABLE: "True"
  DRM_VENDOR_TOKEN_VALUE: "__drm_checker__:all"
  DRM_WIDEVINE_CONTENT_KEY_SPEC: "hdcp:HDCP_NONE,cgms:COPY_NEVER,secure_data_path:false"
  DRM_WIDEVINE_LICENSE_SERVER: "https://license.widevine.com/cenc/getlicense/kkboxjcom"
  DRM_DEFAULT_KEY: ********************************:13da2193bcd455bb894871aec1815047,b817c45e833bb7d628875b7af48fc896:b817c45e833bb7d628875b7af48fc896,de92fbdb64e964267db0fc87847358b9:de92fbdb64e964267db0fc87847358b9,9eff956317e3c6547b70b2556a6bae9f:9eff956317e3c6547b70b2556a6bae9f,5bde558b4e79c706e0aa6c847d81fb3c:5bde558b4e79c706e0aa6c847d81fb3c,0830d39787ce283ab5ed0a9cf4f94f52:0830d39787ce283ab5ed0a9cf4f94f52,52d042fa8ed41f7f3d1f5721cc192022:da332ad81ee6d07ee058c286a0b35d93,2c8b96505815c6bdba5c555c4bb46908:7d78dbb284e72217647ac0b31ad718cb,987809cc666f0e4d89c5fdfcdb22fe2a:908df19d2f9005ac24d5738d13f58d02
  DRM_KEY_SERVER: "http://{{ .Release.Namespace }}-key-server.{{ .Release.Namespace }}/drm_key"
  DRM_LISTEN_PORT: "8080"
  DRM_PLAYREADY_SERVICE_ID: UxbOJRBwskmrA5a0bbvNqA==
  DRM_WIDEVINE_POLICY: playback_duration_seconds:0,can_play:true,can_persist:false
  ENABLED_COLLECT_METRICS: "True"

  K8S_APP_TAG: saku-prep-jon
  S3_LOGS_BUCKET_NAME: jcl-saku-prep-jon-logs
  S3_LOGS_BUCKET_PREFIX: access/
  S3_LOGS_BUCKET_REGION: ap-northeast-1

secretEnv:
  DRM_FAIRPLAY_ASK: "vdt6nLfmhmo8gscrl7PT8Q=="
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  DRM_FAIRPLAY_SERVER_CERTIFICATE: "MIIE+DCCA+CgAwIBAgIIF3MlC3HvFZgwDQYJKoZIhvcNAQEFBQAwfzELMAkGA1UEBhMCVVMxEzARBgNVBAoMCkFwcGxlIEluYy4xJjAkBgNVBAsMHUFwcGxlIENlcnRpZmljYXRpb24gQXV0aG9yaXR5MTMwMQYDVQQDDCpBcHBsZSBLZXkgU2VydmljZXMgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkwHhcNMTkwNTI4MDk0MDU0WhcNMjEwNTI4MDk0MDU0WjCBlDELMAkGA1UEBhMCSlAxLTArBgNVBAoMJEp1cGl0ZXIgVGVsZWNvbW11bmljYXRpb25zIENvLiwgTHRkLjETMBEGA1UECwwKRktVREY2SFEyTjFBMD8GA1UEAww4RmFpclBsYXkgU3RyZWFtaW5nOiBKdXBpdGVyIFRlbGVjb21tdW5pY2F0aW9ucyBDby4sIEx0ZC4wgZ8wDQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBAKE1Q6uKIUTHoAs0U7UZ8wCKUmtr3IRvUyzcaDYBxwAAVI0QbZEX04wg5X1mIEUtsdEN487LEjr7A1muns+UHGl8MxtgBdOm/HXLpKSgdmS+3rsLV/EONK6XtW246IRvlrY+Fg+faMUP+Xg1tmzPtZY/Kw6vpChj8sL9QXW2QHdXAgMBAAGjggHkMIIB4DAMBgNVHRMBAf8EAjAAMB8GA1UdIwQYMBaAFGPkR1TLhXFZRiyDrMxEMWRnAyy+MIHiBgNVHSAEgdowgdcwgdQGCSqGSIb3Y2QFATCBxjCBwwYIKwYBBQUHAgIwgbYMgbNSZWxpYW5jZSBvbiB0aGlzIGNlcnRpZmljYXRlIGJ5IGFueSBwYXJ0eSBhc3N1bWVzIGFjY2VwdGFuY2Ugb2YgdGhlIHRoZW4gYXBwbGljYWJsZSBzdGFuZGFyZCB0ZXJtcyBhbmQgY29uZGl0aW9ucyBvZiB1c2UsIGNlcnRpZmljYXRlIHBvbGljeSBhbmQgY2VydGlmaWNhdGlvbiBwcmFjdGljZSBzdGF0ZW1lbnRzLjA1BgNVHR8ELjAsMCqgKKAmhiRodHRwOi8vY3JsLmFwcGxlLmNvbS9rZXlzZXJ2aWNlcy5jcmwwHQYDVR0OBBYEFG9AtKiPuvzFrxL2yiOzsJpjU/oRMA4GA1UdDwEB/wQEAwIFIDApBgsqhkiG92NkBg0BAwEB/wQXATA3Z2xnOHdxNW02YnJzbWltOGlteGowOQYLKoZIhvdjZAYNAQQBAf8EJwFqM2hiemlmN3pucnI4ZHkzdXF4ZmdlYmE5YmhzeGFqcnlob255YTANBgkqhkiG9w0BAQUFAAOCAQEAShtn7ILu35v/VuXYdjq+fap9tWpJs9K8SfA5uJuTei9Nt5pRj5pSyvqGTt5fqTkneb7SzK0kdhadYOcWZtORpIPeFXfO4AGTAAScuTasioMGHjfIkLu/RmTUFLNV6KNPFRi4BVkTl/LuNivh+Fy8U8MdoUnrV/GW8DJXZUccmF8aQ/tFivujboxw4R4+jslPLZjZGvae+ltALD5RvN6yd+Z79ufFA0fXo3iQxCkZrPeV+iADLMtzJlZ640znrQlXTpGeZ1lsnlvfHfiEXRmpeNjgaB0apzq+nkEPpmCkOrT6nS7GQBqAYbRdTnMwPCSq2smvMQ1r7CdJdPhD/9bWiw=="
  DRM_WIDEVINE_PROVIDER: "name:kkboxjcom,key:7cbc92b31c3e12a8f7035add51b0c47d4de4f80c756e5f52801da788e2282511,iv:f28e5636c78fe9aff6e9051cf7bc092c"
