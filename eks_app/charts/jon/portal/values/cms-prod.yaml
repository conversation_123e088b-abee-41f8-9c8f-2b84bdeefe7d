# Default values for sample.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true
enableTest: false
replicaCount: 1

updateStrategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 25%
    maxUnavailable: 25%

image:
  repository: ************.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-jon-drm-portal
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

volumeMounts:
  - name: drmlog-debug
    mountPath: /var/log/kkstream

imagePullSecrets: []
nameOverride: "cms-portal"
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  className: "alb"
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /healthz
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/ssl-redirect: "443"
    alb.ingress.kubernetes.io/scheme: "internet-facing"
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/subnets: "saku-prod-public-1c,saku-prod-public-1a"
    alb.ingress.kubernetes.io/group.name: 'saku-prod-jon'
    alb.ingress.kubernetes.io/group.order: '2'
    alb.ingress.kubernetes.io/ssl-policy: "ELBSecurityPolicy-TLS13-1-2-2021-06"
    external-dns.alpha.kubernetes.io/aws-weight: '100'
    external-dns.alpha.kubernetes.io/set-identifier: 'saku-prod-eks'
  hosts:
    - host: "jon-cms.kkstream.tech"
      paths:
        - path: /
          pathType: Prefix

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  requests:
    cpu: 200m
    memory: 400Mi
  limits:
    cpu: 300m
    memory: 500Mi

command:
  - node
  - drm-server.js

container:
  port: 8080
  liveness:
    path: /healthz
    port: 8080
    scheme: HTTP
  livenessInitialDelaySeconds: 0
  livenessPeriodSeconds: 10
  readiness:
    path: /readiness
    port: 8080
    scheme: HTTP
  readinessInitialDelaySeconds: 0
  readinessPeriodSeconds: 10

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 50
  targetMemoryUtilizationPercentage: 50

vpa:
  enabled: false
  resources:
    maxAllowed:
      cpu: 800m
      memory: 800Mi
    minAllowed:
      cpu: 100m
      memory: 100Mi

nodeSelector: {}

tolerations: []

affinity: {}

terminationMessage:
  path: /dev/termination-log
  policy: File

configMapEnv:
  DRM_AUTH_SERVER_DOWNLOAD: "https://cms-api.saku.ottfs.com/download"
  DRM_AUTH_SERVER_PLAYBACK: "https://cms-api.saku.ottfs.com/playback"
  DRM_LISTEN_PORT: "8080"
  DRM_DEFAULT_EXPIRE_TIME: "86400"
  DRM_VENDOR_TOKEN_ENABLE: "True"
  DRM_VENDOR_TOKEN_VALUE: "__drm_checker__:all"
  DRM_NULL_TOKEN_ENABLE: "True"
  DRM_NULL_TOKEN_VALUE: "all"
  DRM_DEFAULT_KEY: "adf66f6aeef6df91639a21067464bb83:13da2193bcd455bb894871aec1815047,b817c45e833bb7d628875b7af48fc896:b817c45e833bb7d628875b7af48fc896,de92fbdb64e964267db0fc87847358b9:de92fbdb64e964267db0fc87847358b9,9eff956317e3c6547b70b2556a6bae9f:9eff956317e3c6547b70b2556a6bae9f,5bde558b4e79c706e0aa6c847d81fb3c:5bde558b4e79c706e0aa6c847d81fb3c,0830d39787ce283ab5ed0a9cf4f94f52:0830d39787ce283ab5ed0a9cf4f94f52,52d042fa8ed41f7f3d1f5721cc192022:da332ad81ee6d07ee058c286a0b35d93,2c8b96505815c6bdba5c555c4bb46908:7d78dbb284e72217647ac0b31ad718cb,987809cc666f0e4d89c5fdfcdb22fe2a:908df19d2f9005ac24d5738d13f58d02"
  DRM_PLAYREADY_IS_MIN_SECURITY_LEVEL_150: "False"
  DRM_PLAYREADY_SERVER_URL: http://saku-prod-playready.kkstream.tech/playready/RightsManager.asmx
  DRM_WIDEVINE_LICENSE_SERVER: https://license.widevine.com/cenc/getlicense/kkboxjcom
  
  DRM_KEY_SERVER: "http://{{ .Release.Namespace }}-key-server.{{ .Release.Namespace }}/drm_key"
  DRM_ENABLE_FAIRPLAY: "True"
  DRM_ENABLE_WIDEVINE: "True"
  DRM_ENABLE_PLAYREADY: "True"
  DRM_ENABLE_HLS_CLEARKEY: "True"
  DRM_ENABLE_LOGGER: "True"
  DRM_PLAYREADY_SERVICE_ID: "UxbOJRBwskmrA5a0bbvNqA=="
  DRM_WIDEVINE_CONTENT_KEY_SPEC: "hdcp:HDCP_NONE,cgms:CGMS_NONE,secure_data_path:false"
  DRM_WIDEVINE_POLICY: "playback_duration_seconds:0,can_play:true,can_persist:false"

  K8S_APP_TAG: prod-cms-jon
  S3_LOGS_BUCKET_NAME: jcl-prod-cms-jon-logs
  S3_LOGS_BUCKET_PREFIX: access/
  S3_LOGS_BUCKET_REGION: ap-northeast-1

secretEnv:
  DRM_FAIRPLAY_ASK: "vdt6nLfmhmo8gscrl7PT8Q=="
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  DRM_FAIRPLAY_SERVER_CERTIFICATE: "MIIE+DCCA+CgAwIBAgIIF3MlC3HvFZgwDQYJKoZIhvcNAQEFBQAwfzELMAkGA1UEBhMCVVMxEzARBgNVBAoMCkFwcGxlIEluYy4xJjAkBgNVBAsMHUFwcGxlIENlcnRpZmljYXRpb24gQXV0aG9yaXR5MTMwMQYDVQQDDCpBcHBsZSBLZXkgU2VydmljZXMgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkwHhcNMTkwNTI4MDk0MDU0WhcNMjEwNTI4MDk0MDU0WjCBlDELMAkGA1UEBhMCSlAxLTArBgNVBAoMJEp1cGl0ZXIgVGVsZWNvbW11bmljYXRpb25zIENvLiwgTHRkLjETMBEGA1UECwwKRktVREY2SFEyTjFBMD8GA1UEAww4RmFpclBsYXkgU3RyZWFtaW5nOiBKdXBpdGVyIFRlbGVjb21tdW5pY2F0aW9ucyBDby4sIEx0ZC4wgZ8wDQYJKoZIhvcNAQEBBQADgY0AMIGJAoGBAKE1Q6uKIUTHoAs0U7UZ8wCKUmtr3IRvUyzcaDYBxwAAVI0QbZEX04wg5X1mIEUtsdEN487LEjr7A1muns+UHGl8MxtgBdOm/HXLpKSgdmS+3rsLV/EONK6XtW246IRvlrY+Fg+faMUP+Xg1tmzPtZY/Kw6vpChj8sL9QXW2QHdXAgMBAAGjggHkMIIB4DAMBgNVHRMBAf8EAjAAMB8GA1UdIwQYMBaAFGPkR1TLhXFZRiyDrMxEMWRnAyy+MIHiBgNVHSAEgdowgdcwgdQGCSqGSIb3Y2QFATCBxjCBwwYIKwYBBQUHAgIwgbYMgbNSZWxpYW5jZSBvbiB0aGlzIGNlcnRpZmljYXRlIGJ5IGFueSBwYXJ0eSBhc3N1bWVzIGFjY2VwdGFuY2Ugb2YgdGhlIHRoZW4gYXBwbGljYWJsZSBzdGFuZGFyZCB0ZXJtcyBhbmQgY29uZGl0aW9ucyBvZiB1c2UsIGNlcnRpZmljYXRlIHBvbGljeSBhbmQgY2VydGlmaWNhdGlvbiBwcmFjdGljZSBzdGF0ZW1lbnRzLjA1BgNVHR8ELjAsMCqgKKAmhiRodHRwOi8vY3JsLmFwcGxlLmNvbS9rZXlzZXJ2aWNlcy5jcmwwHQYDVR0OBBYEFG9AtKiPuvzFrxL2yiOzsJpjU/oRMA4GA1UdDwEB/wQEAwIFIDApBgsqhkiG92NkBg0BAwEB/wQXATA3Z2xnOHdxNW02YnJzbWltOGlteGowOQYLKoZIhvdjZAYNAQQBAf8EJwFqM2hiemlmN3pucnI4ZHkzdXF4ZmdlYmE5YmhzeGFqcnlob255YTANBgkqhkiG9w0BAQUFAAOCAQEAShtn7ILu35v/VuXYdjq+fap9tWpJs9K8SfA5uJuTei9Nt5pRj5pSyvqGTt5fqTkneb7SzK0kdhadYOcWZtORpIPeFXfO4AGTAAScuTasioMGHjfIkLu/RmTUFLNV6KNPFRi4BVkTl/LuNivh+Fy8U8MdoUnrV/GW8DJXZUccmF8aQ/tFivujboxw4R4+jslPLZjZGvae+ltALD5RvN6yd+Z79ufFA0fXo3iQxCkZrPeV+iADLMtzJlZ640znrQlXTpGeZ1lsnlvfHfiEXRmpeNjgaB0apzq+nkEPpmCkOrT6nS7GQBqAYbRdTnMwPCSq2smvMQ1r7CdJdPhD/9bWiw=="
  DRM_WIDEVINE_PROVIDER: "name:kkcompanymoment,key:cb2df33e7d9ba82c0ed982d34dc51af6d6baeeb2b07ad0d42bd34ad7fa1e718f,iv:3dafe74faf1e891df79d3714942bfb48"
