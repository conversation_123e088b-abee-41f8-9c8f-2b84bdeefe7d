labels:
  kkstream.io/project: saku
  kkstream.io/env: prod
  app.kubernetes.io/part-of: jon
destination:
  namespace: prod-jon
  server: "https://6C23DB44986B53F74BB0E6536985F1A4.gr7.ap-northeast-1.eks.amazonaws.com"
revision: saku-main
project: saku-prod

valueFiles: #any apps will read default value file for saku prod.
  - values/saku/values-prod.yaml #charts/<app>/values/saku/values-prod.yaml

drmMonitor:
  imageTag: 1.19.1105
keyServer:
  imageTag: 1.19.1105
cms:
  imageTag: 1.23.1201
  valueFiles:
    - values/saku/values-cms-prod.yaml
portal:
  imageTag: 1.23.1201
speke:
  imageTag: 1.23.0925

playready:
  imageTag: v1.0.0


keyServerRestful:
  enabled: true
  imageTag: 1.19.1105
  valueFiles:
    - values/saku/values-prod-restful.yaml
portalRestful:
  enabled: true
  imageTag: 1.23.1201
  valueFiles:
    - values/saku/values-prod-restful.yaml

cronjobRestartPortal:
  enabled: true
