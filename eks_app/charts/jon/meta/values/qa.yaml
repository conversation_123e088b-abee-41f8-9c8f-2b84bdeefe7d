labels:
  kkstream.io/project: saku
  kkstream.io/env: qa
  app.kubernetes.io/part-of: jon
destination:
  namespace: qa-jon
  server: "https://EEF78B478B346E257FE07D9DFC95165D.gr7.ap-northeast-1.eks.amazonaws.com"
revision: saku-main
project: saku-non-prod

valueFiles: #any apps will read default value file for saku qa.
  - values/saku/values-qa.yaml #charts/<app>/values/saku/values-qa.yaml

drmMonitor:
  imageTag: 1.19.1105
keyServer:
  imageTag: 1.24.1017
cms:
  imageTag: 1.23.1201
  valueFiles:
    - values/saku/values-cms-qa.yaml
portal:
  imageTag: 1.23.1201
speke:
  imageTag: 1.23.0925
  
playready:
  imageTag: v1.0.0

keyServerRestful:
  enabled: true
  imageTag: 1.19.1105
  valueFiles:
    - values/saku/values-qa-restful.yaml
portalRestful:
  enabled: true
  imageTag: 1.23.1201
  valueFiles:
    - values/saku/values-qa-restful.yaml
