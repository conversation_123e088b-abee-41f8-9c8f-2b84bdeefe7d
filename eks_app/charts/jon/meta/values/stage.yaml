labels:
  kkstream.io/project: saku
  kkstream.io/env: stage
  app.kubernetes.io/part-of: jon
destination:
  namespace: stage-jon
  server: "https://EEF78B478B346E257FE07D9DFC95165D.gr7.ap-northeast-1.eks.amazonaws.com"
revision: saku-main
project: saku-non-prod

valueFiles: #any apps will read default value file for saku stage.
  - values/saku/values-stage.yaml #charts/<app>/values/saku/values-stage.yaml

drmMonitor:
  imageTag: 1.19.1105
keyServer:
  imageTag: 1.24.1017
cms:
  imageTag: 1.19.0705
  valueFiles:
    - values/saku/values-cms-stage.yaml
portal:
  imageTag: bc8a7847
speke:
  imageTag: 1.22.1227
playready:
  imageTag: v1.0.0

cronjobRestartPortal:
  enabled: false
