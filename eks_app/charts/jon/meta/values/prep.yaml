labels:
  kkstream.io/project: saku
  kkstream.io/env: prep
  app.kubernetes.io/part-of: jon
destination:
  namespace: prep-jon
  server: "https://34EDD38C92651D646F997EA882672428.gr7.ap-northeast-1.eks.amazonaws.com"
revision: saku-main
project: saku-prep

valueFiles: #any apps will read default value file for saku prep.
  - values/saku/values-prep.yaml #charts/<app>/values/saku/values-prep.yaml

drmMonitor:
  imageTag: 1.19.1105
keyServer:
  imageTag: 1.24.1017
cms:
  imageTag: 1.23.1201
  valueFiles:
    - values/saku/values-cms-prep.yaml
portal:
  imageTag: 1.23.1201
speke:
  imageTag: 1.23.0925

playready:
  imageTag: v1.0.0

keyServerRestful:
  enabled: true
  imageTag: 1.19.1105
  valueFiles:
    - values/saku/values-prep-restful.yaml
portalRestful:
  enabled: true
  imageTag: 1.23.1201
  valueFiles:
    - values/saku/values-prep-restful.yaml
