labels:
  app.kubernetes.io/name: jon
destination:
  namespace: jon
  server: https://kubernetes.default.svc
project: jon
soruce:
  repoURL: https://gitlab.kkinternal.com/kkstream/jcl/jcl-infrastructure/helm-charts/jon-k8s.git

revision: HEAD
valueFiles: []

drmMonitor:
#  revision: HEAD
  enabled: true
  imageTag: false
  valueFiles: []

keyServer:
#  revision: HEAD
  enabled: true
  imageTag: false
  valueFiles: []

portal:
#  revision: HEAD
  enabled: true
  imageTag: false
  valueFiles: []

cms:
#  revision: HEAD
  enabled: true
  imageTag: false
  valueFiles: []

speke:
#  revision: HEAD
  enabled: true
  imageTag: false
  valueFiles: []

keyServerRestful:
#  revision: HEAD
  enabled: false
  imageTag: false
  valueFiles: []

portalRestful:
#  revision: HEAD
  enabled: false
  imageTag: false
  valueFiles: []

cronjobRestartPortal:
  enabled: false
  valueFiles: []

playready:
#  revision: HEAD
  enabled: true
  imageTag: false
  valueFiles: []
