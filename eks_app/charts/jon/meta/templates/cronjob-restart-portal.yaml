{{- if .Values.cronjobRestartPortal.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-cronjob-restart-portal
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/cronjob-restart-portal
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.cronjobRestartPortal.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-cronjob-restart-portal
      valueFiles:
        {{- if not .Values.cronjobRestartPortal.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else}}
          {{- range .Values.cronjobRestartPortal.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end}}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
{{- end }}