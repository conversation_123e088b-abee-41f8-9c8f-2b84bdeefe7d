{{- if .Values.drmMonitor.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-drm-monitor
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/drm-monitor
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.keyServer.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-drm-monitor
      {{- with .Values.drmMonitor.imageTag }}
      parameters:
        - name: image.tag
          value: {{ . | quote }}
      {{- end }}
      valueFiles: 
        {{- if not .Values.drmMonitor.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else }}
          {{- range .Values.drmMonitor.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end }}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
{{- end }}