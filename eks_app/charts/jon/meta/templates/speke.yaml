{{- if .Values.speke.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-speke
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/speke
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.speke.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-speke
      {{- with .Values.speke.imageTag }}
      parameters:
        - name: image.tag
          value: {{ . | quote }}
      {{- end }}
      valueFiles:
        {{- if not .Values.speke.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else}}
          {{- range .Values.speke.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end}}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
{{- end }}