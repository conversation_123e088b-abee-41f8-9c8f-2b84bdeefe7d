{{- if .Values.playready.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-playready
  labels: {{- toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{- toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/playready
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.playready.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-playready
      {{- with .Values.playready.imageTag }}
      parameters:
        - name: image.tag
          value: {{ . | quote }}
      {{- end }}
      valueFiles:
        {{- if not .Values.playready.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else }}
          {{- range .Values.playready.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end }}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
{{- end }}