{{- if .Values.portalRestful.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-portal-restful
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/portal
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.portalRestful.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-portal-restful
      {{- with .Values.portalRestful.imageTag }}
      parameters:
        - name: image.tag
          value: {{ . | quote }}
      {{- end }}
      valueFiles:
        {{- if not .Values.portalRestful.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else}}
          {{- range .Values.portalRestful.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end}}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
{{- end }}