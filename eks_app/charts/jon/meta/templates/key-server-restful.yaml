{{- if .Values.keyServerRestful.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-key-server-restful
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/key-server
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.keyServerRestful.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-key-server-restful
      {{- with .Values.keyServerRestful.imageTag }}
      parameters:
        - name: image.tag
          value: {{ . | quote }}
      {{- end }}
      valueFiles:
        {{- if not .Values.keyServerRestful.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else}}
          {{- range .Values.keyServerRestful.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end}}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
{{- end }}