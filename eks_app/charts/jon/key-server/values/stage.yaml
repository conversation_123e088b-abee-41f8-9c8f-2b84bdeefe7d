# Default values for sample.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true
enableTest: false
replicaCount: 1

updateStrategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 25%
    maxUnavailable: 25%

image:
  repository: ************.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-jon-key-server
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "1.24.1017"

volumeMounts: []

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: false

resources:
  limits:
    cpu: 300m
    memory: 300Mi
  requests:
    cpu: 200m
    memory: 100Mi

command:
  - node
  - server.js

container:
  port: 8000
  liveness:
    path: /healthz
    port: 8000
    scheme: HTTP
  livenessInitialDelaySeconds: 0
  livenessPeriodSeconds: 10
  readiness:
    path: /readiness
    port: 8000
    scheme: HTTP
  readinessInitialDelaySeconds: 0
  readinessPeriodSeconds: 10

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 50
  targetMemoryUtilizationPercentage: 50

vpa:
  enabled: false
  resources:
    maxAllowed:
      cpu: 400m
      memory: 400Mi
    minAllowed:
      cpu: 100m
      memory: 100Mi

nodeSelector: {}

tolerations: []

affinity: {}

terminationMessage:
  path: /dev/termination-log
  policy: File

configMapEnv:
  DRM_KEY_SERVER_PORT: 8000

secretEnv:
