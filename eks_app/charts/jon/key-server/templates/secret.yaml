{{- if .Values.enabled }}
{{- $fullname := include "jon-key-server.fullname" . -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $fullname }}-env
  labels:
    {{- include "jon-key-server.labels" . | nindent 4 }}
type: Opaque
data:
  {{- range $key, $val := .Values.secretEnv }}
    {{- if $val }}
      {{ $key }}: {{ $val | b64enc }}
    {{- else }}
      {{ $key }}: ""
    {{- end }}
  {{- end }}
{{- end }}
