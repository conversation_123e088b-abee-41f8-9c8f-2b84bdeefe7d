{{- if .Values.enableTest }}
apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "jon-key-server.fullname" . }}-test-connection"
  labels:
    {{- include "jon-key-server.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "jon-key-server.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
{{- end }}
