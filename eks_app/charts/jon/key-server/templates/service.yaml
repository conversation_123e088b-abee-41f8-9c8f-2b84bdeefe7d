{{- if .Values.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "jon-key-server.fullname" . }}
  labels:
    {{- include "jon-key-server.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "jon-key-server.selectorLabels" . | nindent 4 }}
{{- end }}
