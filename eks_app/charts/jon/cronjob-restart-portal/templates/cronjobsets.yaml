{{- if .Values.enabled }}
---
kind: ServiceAccount
apiVersion: v1
metadata:
  name: restart-deployment-{{ .Values.deoloymentName }}
  namespace: {{ .Values.namespaces }}
---
kind: Role
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: restart-deployment-{{ .Values.deoloymentName }}
  namespace: {{ .Values.namespaces }}
rules:
  - apiGroups: ["apps", "v1"]
    resources: ["deployments"]
    resourceNames: ["{{ .Values.deoloymentName }}"]
    verbs: ["get", "patch", "list", "watch"] 
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: "restart-deployment-{{ .Values.deoloymentName }}"
  namespace: {{ .Values.namespaces }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: "restart-deployment-{{ .Values.deoloymentName }}"
subjects:
  - kind: ServiceAccount
    name: "restart-deployment-{{ .Values.deoloymentName }}"
    namespace: {{ .Values.namespaces }}

---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: "restart-deployment-{{ .Values.deoloymentName }}"
  namespace: {{ .Values.namespaces }}
spec:
  failedJobsHistoryLimit: 3 #cronjob will keep track of last 3 failed jobs
  successfulJobsHistoryLimit: 1 #cronjob will keep track of last successful job
  concurrencyPolicy: Forbid
  schedule: '0 0/4 * * *' 
  jobTemplate:
    spec:
      backoffLimit: 3 # this has very low chance of failing, as all this does is prompt kubernetes to schedule new replica set for the deployment
      activeDeadlineSeconds: 300 # timeout, makes most sense with "waiting for rollout" variant specified below
      template:
        spec:
          serviceAccountName: "restart-deployment-{{ .Values.deoloymentName }}"
          restartPolicy: Never
          containers:
            - name: kubectl
              image: bitnami/kubectl:1.25.15
              command:
              - bash
              - -c
              - >-
                kubectl rollout restart deployment/{{ .Values.deoloymentName }} &&
                kubectl rollout status deployment/{{ .Values.deoloymentName }}
{{- end }}