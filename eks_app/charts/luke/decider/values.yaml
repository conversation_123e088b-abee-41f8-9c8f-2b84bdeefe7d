# Default values for luke-job-decider.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
enabled: true
enableTest: false
replicaCount: 1

image:
  repository: ************.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-luke-decider
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

resources:
  limits:
    cpu: 200m
    memory: 250Mi
  requests:
    cpu: 150m
    memory: 150Mi
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as <PERSON>kube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 50
  targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: topology.kubernetes.io/zone
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app.kubernetes.io/instance:  "{{ .Release.Name }}"
  - maxSkew: 1
    topologyKey: kubernetes.io/hostname
    whenUnsatisfiable: DoNotSchedule
    labelSelector:
      matchLabels:
        app.kubernetes.io/instance: "{{ .Release.Name }}"

encoderConfigName: "{{ .Release.Namespace }}-encoder-config-json"

configMapEnv:
  LUKE_CALLBACK_URL: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/encoding_reports"
  LUKE_ENCODING_SOURCE_URL: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/encoding_source"
  LUKE_GET_SUBTITLE_URLS_URL: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/get_subtitle_urls"
  LUKE_REPORT_ERROR_URL: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/report_error"
  LUKE_ENCODING_MANAGER_URL: "http://{{ .Release.Namespace }}-job-manager.{{ .Release.Namespace }}/api/v1/jobs"

  AES128_SYSTEM_ID: ce7acac260e0f1fcc2d343b6fcb9902a
  CMD_EXECUTION_TIMEOUT: "86400"
  CMD_MAX_RETRY: "3"
  CMD_WAITING_TIMEOUT: "2592000"
  CMS_API_HOSTNAME: http://wtf/
  DUMMY_KEY: da332ad81ee6d07ee058c286a0b35d93
  DUMMY_KEY_ID: 52d042fa8ed41f7f3d1f5721cc192022
  JOB_EXECUTION_TIMEOUT: "345600"
  JOB_TASK_EXCHANGE_RATE: "1000"
  JOB_WAITING_TIMEOUT: "2592000"
  NATALIE_WORKER_HEARTBEAT_INTERVAL: "300"
  NATALIE_WORKER_MIN_ROOT_STORAGE: "1"
  NATALIE_WORKER_MIN_WORKABLE_STORAGE: "1"
  NATALIE_WORKER_RESULT_CMD_MAX_LENGTH: "99999999"
  NATALIE_WORKER_ROOT_DIRECTORY: /
  NATALIE_WORKER_WORKING_DIRECTORY: /tmp
  PATH_MP4EXTRACT: mp4extract
  PLAYREADY_LICENSE_URL: http://drm.kkstream.tech/rightsmanager.asmx
  PLAYREADY_LOGIN_URL: http://drm.kkstream.tech/rightsmanager.asmx
  PLAYREADY_SYSTEM_ID: 9a04f07998404286ab92e65be0885f95
  PLAYREADY_VP_SERVICE_ID: 25ce1653701049b2ab0396b46dbbcda8
  S3_BUCKET_DEPLOY: jcl-test-theater #change
  S3_BUCKET_WORKING: jcl-test-studio #change
  SLACK_WEBHOOK_URL: ***************************************************************************** #change
  START_TASK_MAX_RETRY: "5"
  SWF_ACTIVITY_HEARTBEAT_INTERVAL: "900"
  SWF_ACTIVITY_HEARTBEAT_MAX_RETRY: "3"
  SWF_ACTIVITY_HEARTBEAT_RETRY_INTERVAL: "10"
  SWF_ACTIVITY_HEARTBEAT_TIMEOUT: "3600"
  SWF_ACTIVITY_NAME: Cmd
  SWF_ACTIVITY_TASK_LIST: natalie
  SWF_ACTIVITY_VERSION: "0.1"
  SWF_DOMAIN: jcl-test-encoding #change
  SWF_JOB_WORKFLOW_NAME: Job
  SWF_JOB_WORKFLOW_VERSION: "0.1"
  SWF_MAX_INPUT_DATA_SIZE: "32000"
  SWF_MAX_REASON_SIZE: "256"
  SWF_MAX_RETURN_DATA_SIZE: "32000"
  SWF_REGION: ap-northeast-1
  SWF_REQUEST_MAX_RETRY: "3"
  SWF_TASK_WORKFLOW_NAME: Task
  SWF_TASK_WORKFLOW_VERSION: "0.1"
  SWF_WORKFLOW_CHILD_POLICY: REQUEST_CANCEL
  SWF_WORKFLOW_EXECUTION_TIMEOUT: "60"
  SWF_WORKFLOW_TASK_LIST: natalie
  TASK_MAX_RETRY: "0"
  TMP_DIR2: /tmp
  WIDEVINE_SYSTEM_ID: edef8ba979d64acea3c827dcd51d21ed
  # P1:saku-test-encoding-premium;P2:saku-test-encoding-priority
  # by project
  SWF_DOMAIN_MAPPING: ""

secret:
  awsData:
    credentials:
      enabled: false
