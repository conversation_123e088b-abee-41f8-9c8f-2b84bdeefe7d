{{- if .Values.enabled }}
{{- $fullname := include "luke-job-decider.fullname" . -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullname }}-env
  labels:
    {{- include "luke-job-decider.labels" . | nindent 4 }}
data:
  {{- range $key, $val := .Values.configMapEnv }}
    {{- if $val }}
      {{ $key }}: {{ tpl $val $ | quote }}
    {{- else }}
      {{ $key }}: ""
    {{- end }}
  {{- end }}
{{- end }}
