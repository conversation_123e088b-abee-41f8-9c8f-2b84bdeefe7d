{{- if and .Values.enabled .Values.serviceAccount.create -}}
{{ $fullname := include "luke-job-decider.fullname" . }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "luke-job-decider.serviceAccountName" . }}
  labels:
    {{- include "luke-job-decider.labels" . | nindent 4 }}
rules:
- apiGroups: ["*"]
  resources: ["*"]
  verbs: ["*"]
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ include "luke-job-decider.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "luke-job-decider.labels" . | nindent 4 }}
subjects:
- kind: ServiceAccount
  namespace: {{ .Release.Namespace }}
  name: {{ include "luke-job-decider.serviceAccountName" . }}
roleRef:
  kind: ClusterRole
  name: {{ include "luke-job-decider.serviceAccountName" . }}
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "luke-job-decider.serviceAccountName" . }}
  labels:
    {{- include "luke-job-decider.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
