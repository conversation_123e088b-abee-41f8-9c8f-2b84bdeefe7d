autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

resources:
  limits:
    cpu: 200m
    memory: 250Mi
  requests:
    cpu: 150m
    memory: 150Mi

encoderConfigName: "{{ .Release.Namespace }}-encoder-priority"

configMapEnv:
  S3_BUCKET_DEPLOY: kks-prep-saku-theater
  S3_BUCKET_WORKING: kks-prep-saku-studio
  SLACK_WEBHOOK_URL: *****************************************************************************
  SWF_DOMAIN: saku2-stag-encoding-priority


secret:
  awsData:
    credentials:
      enabled: true
      AWS_ACCESS_KEY_ID: ********************
      AWS_SECRET_ACCESS_KEY: sC/yXOXm+vMQU14UCk0tgqoGwhe9jOtIQlwc/ofs