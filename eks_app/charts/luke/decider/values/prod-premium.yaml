autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 50

resources:
  limits:
    cpu: 200m
    memory: 250Mi
  requests:
    cpu: 150m
    memory: 150Mi

encoderConfigName: "{{ .Release.Namespace }}-encoder-premium"

configMapEnv:
  S3_BUCKET_DEPLOY: kks-prod-saku-theater
  S3_BUCKET_WORKING: kks-prod-saku-studio
  SLACK_WEBHOOK_URL: *****************************************************************************
  SWF_DOMAIN: saku-prod-encoding-premium

secret:
  awsData:
    credentials:
      enabled: true
      AWS_ACCESS_KEY_ID: ********************
      AWS_SECRET_ACCESS_KEY: wHINkkIQTF7yrcs7z6bJL9dUwbbFCQ7gcmoJQi+Z