image:
  tag: 1.24.1219

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

resources:
  limits:
    cpu: 200m
    memory: 250Mi
  requests:
    cpu: 150m
    memory: 150Mi

encoderConfigName: "{{ .Release.Namespace }}-encoder"

configMapEnv:
  S3_BUCKET_DEPLOY: kks-qa-saku-theater
  S3_BUCKET_WORKING: kks-qa-saku-studio
  SLACK_WEBHOOK_URL: *****************************************************************************
  SWF_DOMAIN: saku-qa-encoding

secret:
  awsData:
    credentials:
      enabled: true
      AWS_ACCESS_KEY_ID: ********************
      AWS_SECRET_ACCESS_KEY: uZOY9zIvZlDWo32/rJ536HvBa/BpQEiSwtQutDLU
