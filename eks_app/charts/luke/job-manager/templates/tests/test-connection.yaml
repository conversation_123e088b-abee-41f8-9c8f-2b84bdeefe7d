{{- if .Values.enableTest }}
apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "luke-job-manager.fullname" . }}-test-connection"
  labels:
    {{- include "luke-job-manager.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "luke-job-manager.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
{{- end }}
