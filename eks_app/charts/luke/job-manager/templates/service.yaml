{{- if .Values.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "luke-job-manager.fullname" . }}
  labels:
    {{- include "luke-job-manager.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "luke-job-manager.selectorLabels" . | nindent 4 }}
{{- end }}
