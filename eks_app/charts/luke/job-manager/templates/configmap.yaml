{{- if .Values.enabled }}
{{- $fullname := include "luke-job-manager.fullname" . -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullname }}-env
  labels:
    {{- include "luke-job-manager.labels" . | nindent 4 }}
data:
  {{- range $key, $val := .Values.configMapEnv }}
    {{- if $val }}
      {{ $key }}: {{ tpl $val $ | quote }}
    {{- else }}
      {{ $key }}: ""
    {{- end }}
  {{- end }}

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullname }}-config-json
  labels:
    {{- include "luke-job-manager.labels" . | nindent 4 }}
data:
{{ tpl (.Files.Glob "configs/*.json").AsConfig . | indent 2 }}
{{- end }}
