image:
  tag: 1.25.0325

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 50

resources:
  limits:
    cpu: 250m
    memory: 400Mi
  requests:
    cpu: 200m
    memory: 300Mi

serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/saku/stage/luke/saku-stage-luke-irsa-role"

KEY_SERVER_URL: "http://stage-jon-key-server.stage-jon/drm_key"

configMapEnv:
  S3_BUCKET_DEPLOY: kks-stag-saku-theater
  S3_BUCKET_WORKING: kks-stag-saku-studio
  SLACK_WEBHOOK_URL: *****************************************************************************
  SWF_DOMAIN: saku-stag-encoding
  SWF_DOMAIN_MAPPING: P1:saku-stag-encoding-premium;P2:saku-stag-encoding-priority

configMapJson:
  workflow:
    sourceBuckets:
      saku: "kks-stag-saku-source-upload"
      telasa: "telasa-test-source-upload"
      share: "saku-stag-telasa-shared-source-upload"
    share:
      workingBucket: "saku-stag-shared-studio"
      vendorName: "saku"
    saku:
      workingBucket: "kks-stag-saku-studio"
      mezzanineBucket: "kks-stag-saku-theater"
      trailerBucket: "kks-stag-saku-theater"
      keyServerUrl: "http://stage-jon-key-server.stage-jon/drm_key"
      vendorName: "saku"
    telasa:
      workingMezzanineBucket: "videopass-testing-studio"
      workingTrailerBucket: "videopass-testing-cuttingroom"
      mezzanineBucket: "videopass-testing-theater"
      trailerBucket: "videopass-testing-showroom"
      keyServerUrl: "http://stage-jon-key-server.stage-jon/get_key_pair.php"
      vendorName: "saku"
