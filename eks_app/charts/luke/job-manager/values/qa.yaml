image:
  tag: 1.25.0325

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 50

resources:
  limits:
    cpu: 250m
    memory: 400Mi
  requests:
    cpu: 200m
    memory: 300Mi

affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - podAffinityTerm:
          labelSelector:
            matchExpressions:
              - key: service
                operator: In
                values:
                  - general
          topologyKey: topology.kubernetes.io/zone
        weight: 100
      - podAffinityTerm:
          labelSelector:
            matchExpressions:
              - key: service
                operator: In
                values:
                  - general
          topologyKey: kubernetes.io/hostname
        weight: 99

KEY_SERVER_URL: "http://qa-jon-key-server.qa-jon/drm_key"
VendorToken: "saku3"

configMapEnv:
  S3_BUCKET_DEPLOY: kks-qa-saku-theater
  S3_BUCKET_WORKING: kks-qa-saku-studio
  SLACK_WEBHOOK_URL: *****************************************************************************
  SWF_DOMAIN: saku-qa-encoding
  SWF_DOMAIN_MAPPING: P1:saku-qa-encoding-premium;P2:saku-qa-encoding-priority

configMapJson:
  workflow:
    sourceBuckets:
      saku: "kks-qa-saku-source-upload"
      telasa: "telasa-qa-source-upload"
      share: "saku-qa-telasa-shared-source-upload"
    share:
      workingBucket: "saku-qa-shared-studio"
      vendorName: "saku3"
    saku:
      workingBucket: "kks-qa-saku-studio"
      mezzanineBucket: "kks-qa-saku-theater"
      trailerBucket: "kks-qa-saku-theater"
      keyServerUrl: "http://qa-jon-key-server.qa-jon/drm_key"
      vendorName: "saku3"
    telasa:
      workingMezzanineBucket: "videopass-qa-studio"
      workingTrailerBucket: "videopass-qa-cuttingroom"
      mezzanineBucket: "videopass-qa-theater"
      trailerBucket: "videopass-qa-showroom"
      keyServerUrl: "http://qa-jon-key-server.qa-jon/get_key_pair.php"
      vendorName: "saku3"

secret:
  awsData:
    credentials:
      enabled: true
      AWS_ACCESS_KEY_ID: ********************
      AWS_SECRET_ACCESS_KEY: uZOY9zIvZlDWo32/rJ536HvBa/BpQEiSwtQutDLU
