autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 50

resources:
  limits:
   cpu: 250m
   memory: 400Mi
  requests:
   cpu: 200m
   memory: 300Mi

KEY_SERVER_URL: "http://prep-jon-key-server.prep-jon/drm_key"
VendorToken: "saku2"

configMapEnv:
  S3_BUCKET_DEPLOY: kks-prep-saku-theater
  S3_BUCKET_WORKING: kks-prep-saku-studio
  SLACK_WEBHOOK_URL: *****************************************************************************
  SWF_DOMAIN: saku2-stag-encoding
  SWF_DOMAIN_MAPPING: P1:saku2-stag-encoding-premium;P2:saku2-stag-encoding-priority

configMapJson:
  workflow:
    sourceBuckets:
      saku: "kks-prep-saku-source-upload"
      telasa: "telasa-stage-source-upload"
      share: "saku-prep-telasa-shared-source-upload"
    share:
      workingBucket: "saku-prep-shared-studio"
      vendorName: "saku2"
    saku:
      workingBucket: "kks-prep-saku-studio"
      mezzanineBucket: "kks-prep-saku-theater"
      trailerBucket: "kks-prep-saku-theater"
      keyServerUrl: "http://prep-jon-key-server.prep-jon/drm_key"
      vendorName: "saku2"
    telasa:
      workingMezzanineBucket: "videopass-staging-studio"
      workingTrailerBucket: "videopass-staging-cuttingroom"
      mezzanineBucket: "videopass-staging-theater"
      trailerBucket: "videopass-staging-showroom"
      keyServerUrl: "http://prep-jon-key-server.prep-jon/get_key_pair.php"
      vendorName: "saku2"

secret:
  awsData:
    credentials:
      enabled: true
      AWS_ACCESS_KEY_ID: ********************
      AWS_SECRET_ACCESS_KEY: sC/yXOXm+vMQU14UCk0tgqoGwhe9jOtIQlwc/ofs