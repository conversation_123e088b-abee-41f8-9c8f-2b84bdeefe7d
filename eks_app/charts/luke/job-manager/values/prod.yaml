autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 50

resources:
  limits:
   cpu: 250m
   memory: 400Mi
  requests:
   cpu: 200m
   memory: 300Mi

KEY_SERVER_URL: "http://prod-jon-key-server.prod-jon/drm_key"
VendorToken: "saku"

configMapEnv:
  S3_BUCKET_DEPLOY: kks-prod-saku-theater
  S3_BUCKET_WORKING: kks-prod-saku-studio
  SLACK_WEBHOOK_URL: *****************************************************************************
  SWF_DOMAIN: saku-prod-encoding
  SWF_DOMAIN_MAPPING: P1:saku-prod-encoding-premium;P2:saku-prod-encoding-priority

configMapJson:
  workflow:
    sourceBuckets:
      saku: "kks-prod-saku-source-upload"
      telasa: "telasa-prod-source-upload"
      share: "saku-prod-telasa-shared-source-upload"
    share:
      workingBucket: "saku-prod-shared-studio"
    saku:
      workingBucket: "kks-prod-saku-studio"
      mezzanineBucket: "kks-prod-saku-theater"
      trailerBucket: "kks-prod-saku-theater"
      keyServerUrl: "http://prod-jon-key-server.prod-jon/drm_key"
    telasa:
      workingMezzanineBucket: "videopass-studio"
      workingTrailerBucket: "videopass-cuttingroom"
      mezzanineBucket: "videopass-theater"
      trailerBucket: "videopass-showroom"
      keyServerUrl: "http://prod-jon-key-server.prod-jon/get_key_pair.php"

secret:
  awsData:
    credentials:
      enabled: true
      AWS_ACCESS_KEY_ID: ********************
      AWS_SECRET_ACCESS_KEY: wHINkkIQTF7yrcs7z6bJL9dUwbbFCQ7gcmoJQi+Z