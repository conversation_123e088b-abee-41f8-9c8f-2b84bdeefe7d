[{"rule": {"source_bucket": "{{ .Values.configMapJson.workflow.sourceBuckets.share }}"}, "profile_config": "/luke-config/profile_config_telasa.json", "job_generation_config": {"name": "share", "config": {"vendor_name": "{{ .Values.configMapJson.workflow.share.vendorName }}", "saku": {"working_bucket": "{{ .Values.configMapJson.workflow.share.workingBucket }}", "mezzanine_bucket": "{{ .Values.configMapJson.workflow.saku.mezzanineBucket }}", "trailer_bucket": "{{ .Values.configMapJson.workflow.saku.trailerBucket }}", "key_server_url": "{{ .Values.configMapJson.workflow.saku.keyServerUrl }}", "vendor_name": "{{ .Values.configMapJson.workflow.saku.vendorName }}", "widevine_provider": "{{ .Values.configMapJson.workflow.saku.widevineProvider }}", "playready_la_url": "{{ .Values.configMapJson.workflow.saku.playreadyLaUrl }}", "playready_lui_url": "{{ .Values.configMapJson.workflow.saku.playreadyLuiUrl }}", "thumbnail_seeking_override": {"image_height": 240}, "legacy_package_mode": false}, "telasa": {"working_mezzanine_bucket": "{{ .Values.configMapJson.workflow.telasa.workingMezzanineBucket }}", "working_trailer_bucket": "{{ .Values.configMapJson.workflow.telasa.workingTrailerBucket }}", "mezzanine_bucket": "{{ .Values.configMapJson.workflow.telasa.mezzanineBucket }}", "trailer_bucket": "{{ .Values.configMapJson.workflow.telasa.trailerBucket }}", "key_server_url": "{{ .Values.configMapJson.workflow.telasa.keyServerUrl }}", "vendor_name": "{{ .Values.configMapJson.workflow.telasa.vendorName }}", "widevine_provider": "{{ .Values.configMapJson.workflow.telasa.widevineProvider }}", "playready_la_url": "{{ .Values.configMapJson.workflow.telasa.playreadyLaUrl }}", "playready_lui_url": "{{ .Values.configMapJson.workflow.telasa.playreadyLuiUrl }}", "legacy_package_mode": true}}}}, {"rule": {"source_bucket": "{{ .Values.configMapJson.workflow.sourceBuckets.saku }}"}, "profile_config": "/luke-config/config.json", "job_generation_config": {"name": "saku", "config": {"working_bucket": "{{ .Values.configMapJson.workflow.saku.workingBucket }}", "deploy_bucket": "{{ .Values.configMapJson.workflow.saku.mezzanineBucket }}", "key_server_url": "{{ .Values.configMapJson.workflow.saku.keyServerUrl }}", "vendor_name": "{{ .Values.configMapJson.workflow.saku.vendorName }}", "widevine_provider": "{{ .Values.configMapJson.workflow.saku.widevineProvider }}", "playready_la_url": "{{ .Values.configMapJson.workflow.saku.playreadyLaUrl }}", "playready_lui_url": "{{ .Values.configMapJson.workflow.saku.playreadyLuiUrl }}"}}}, {"rule": {"source_bucket": "{{ .Values.configMapJson.workflow.sourceBuckets.telasa }}"}, "profile_config": "/luke-config/profile_config_telasa.json", "job_generation_config": {"name": "telasa", "config": {"working_mezzanine_bucket": "{{ .Values.configMapJson.workflow.telasa.workingMezzanineBucket }}", "working_trailer_bucket": "{{ .Values.configMapJson.workflow.telasa.workingTrailerBucket }}", "mezzanine_bucket": "{{ .Values.configMapJson.workflow.telasa.mezzanineBucket }}", "trailer_bucket": "{{ .Values.configMapJson.workflow.telasa.trailerBucket }}", "key_server_url": "{{ .Values.configMapJson.workflow.telasa.keyServerUrl }}", "vendor_name": "{{ .Values.configMapJson.workflow.telasa.vendorName }}", "widevine_provider": "{{ .Values.configMapJson.workflow.telasa.widevineProvider }}", "playready_la_url": "{{ .Values.configMapJson.workflow.telasa.playreadyLaUrl }}", "playready_lui_url": "{{ .Values.configMapJson.workflow.telasa.playreadyLuiUrl }}", "legacy_package_mode": true}}}]