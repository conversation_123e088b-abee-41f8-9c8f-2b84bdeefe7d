{{- if .Values.encoderPremium.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-encoder-premium
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/encoder
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.encoderPremium.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-encoder-premium
      {{- with .Values.encoderPremium.imageTag }}
      parameters:
        - name: image.tag
          value: {{ . | quote }}
      {{- end }}
      valueFiles:
        {{- if not .Values.encoderPremium.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else}}
          {{- range .Values.encoderPremium.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end}}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
{{- end}}