{{- if .Values.internalAPI.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-internal-api
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/internal-api
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.internalAPI.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-internal-api
      {{- with .Values.internalAPI.imageTag }}
      parameters:
        - name: image.tag
          value: {{ . | quote }}
      {{- end }}
      valueFiles:
        {{- if not .Values.internalAPI.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else}}
          {{- range .Values.internalAPI.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end}}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
{{- end}}