{{- if .Values.publicAPI.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-public-api
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/public-api
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.publicAPI.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-public-api
      {{- with .Values.publicAPI.imageTag }}
      parameters:
        - name: image.tag
          value: {{ . | quote }}
      {{- end }}
      valueFiles:
        {{- if not .Values.publicAPI.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else}}
          {{- range .Values.publicAPI.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end}}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
{{- end}}