{{- if .Values.deciderPremium.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-decider-premium
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/decider
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.deciderPremium.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-decider-premium
      {{- with .Values.deciderPremium.imageTag }}
      parameters:
        - name: image.tag
          value: {{ . | quote }}
      {{- end }}
      valueFiles:
        {{- if not .Values.deciderPremium.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else}}
          {{- range .Values.deciderPremium.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end}}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
{{- end}}