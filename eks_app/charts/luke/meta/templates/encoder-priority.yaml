{{- if .Values.encoderPriority.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-encoder-priority
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/encoder
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.encoderPriority.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-encoder-priority
      {{- with .Values.encoderPriority.imageTag }}
      parameters:
        - name: image.tag
          value: {{ . | quote }}
      {{- end }}
      valueFiles:
        {{- if not .Values.encoderPriority.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else}}
          {{- range .Values.encoderPriority.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end}}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
{{- end}}