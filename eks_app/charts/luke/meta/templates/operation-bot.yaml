{{- if .Values.operationBot.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-operation-bot
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/operation-bot
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.operationBot.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-operation-bot
      {{- with .Values.operationBot.imageTag }}
      parameters:
        - name: image.tag
          value: {{ . | quote }}
      {{- end }}
      valueFiles:
        {{- if not .Values.operationBot.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else}}
          {{- range .Values.operationBot.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end}}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
{{- end}}