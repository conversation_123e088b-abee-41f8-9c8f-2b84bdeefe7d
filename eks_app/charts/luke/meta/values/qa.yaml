labels:
  kkstream.io/project: saku
  kkstream.io/env: qa
  app.kubernetes.io/part-of: luke
destination:
  namespace: qa-luke
  server: 'https://EEF78B478B346E257FE07D9DFC95165D.gr7.ap-northeast-1.eks.amazonaws.com'
revision: saku-main
project: saku-non-prod
valueFiles:
  - values/saku/values-qa.yaml

publicAPI:
  enabled: true
  imageTag: "1.25.0103"

internalAPI:
  enabled: true
  imageTag: "1.25.0305"

jobManager:
  enabled: true
  imageTag: "1.25.0311"

decider:
  enabled: true
  imageTag: "1.24.1219"

deciderPriority:
  enabled: true
  imageTag: "1.24.1219"
  valueFiles:
    - values/saku/values-qa-priority.yaml

deciderPremium:
  enabled: true
  imageTag: "1.24.1219"
  valueFiles:
    - values/saku/values-qa-premium.yaml

encoder:
  enabled: true
  imageTag: "1.25.0305"

encoderPriority:
  enabled: true
  imageTag: "1.25.0305"
  valueFiles:
    - values/saku/values-qa-priority.yaml

encoderPremium:
  enabled: true
  imageTag: "1.25.0305"
  valueFiles:
    - values/saku/values-qa-premium.yaml