labels:
  kkstream.io/project: saku
  kkstream.io/env: prep
  app.kubernetes.io/part-of: luke
destination:
  namespace: prep-luke
  server: 'https://34EDD38C92651D646F997EA882672428.gr7.ap-northeast-1.eks.amazonaws.com'
revision: saku-main
project: saku-prep
valueFiles:
  - values/saku/values-prep.yaml

publicAPI:
  enabled: true
  imageTag: "1.25.0103"

internalAPI:
  enabled: true
  imageTag: "1.25.0305"

jobManager:
  enabled: true
  imageTag: "1.25.0311"

decider:
  enabled: true
  imageTag: "1.24.1219"

deciderPriority:
  enabled: true
  imageTag: "1.24.1219"
  valueFiles:
    - values/saku/values-prep-priority.yaml

deciderPremium:
  enabled: true
  imageTag: "1.24.1219"
  valueFiles:
    - values/saku/values-prep-premium.yaml

encoder:
  enabled: true
  imageTag: "1.25.0305"

encoderPriority:
  enabled: true
  imageTag: "1.25.0305"
  valueFiles:
    - values/saku/values-prep-priority.yaml

encoderPremium:
  enabled: true
  imageTag: "1.25.0305"
  valueFiles:
    - values/saku/values-prep-premium.yaml
