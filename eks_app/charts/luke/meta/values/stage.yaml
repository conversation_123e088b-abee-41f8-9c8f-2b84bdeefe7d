labels:
  kkstream.io/project: saku
  kkstream.io/env: stage
  app.kubernetes.io/part-of: luke
destination:
  namespace: stage-luke
  server: 'https://EEF78B478B346E257FE07D9DFC95165D.gr7.ap-northeast-1.eks.amazonaws.com'
revision: saku-main
project: saku-non-prod
valueFiles:
  - values/saku/values-stage.yaml

publicAPI:
  enabled: true
  imageTag: "1.25.0103"

internalAPI:
  enabled: true
  imageTag: "1.25.0305"

jobManager:
  enabled: true
  imageTag: "1.25.0311"

decider:
  enabled: true
  imageTag: "1.24.1219"

deciderPriority:
  enabled: true
  imageTag: "1.24.1219"
  valueFiles:
    - values/saku/values-stage-priority.yaml

deciderPremium:
  enabled: true
  imageTag: "1.24.1219"
  valueFiles:
    - values/saku/values-stage-premium.yaml

encoder:
  enabled: true
  imageTag: "1.25.0305"

encoderPriority:
  enabled: true
  imageTag: "1.25.0305"
  valueFiles:
    - values/saku/values-stage-priority.yaml

encoderPremium:
  enabled: true
  imageTag: "1.25.0305"
  valueFiles:
    - values/saku/values-stage-premium.yaml

operationBot:
  enabled: true
  imageTag: "1.25.0121"
  valueFiles:
    - values/saku/values-stage.yaml