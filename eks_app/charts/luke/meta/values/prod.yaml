labels:
  kkstream.io/project: saku
  kkstream.io/env: prod
  app.kubernetes.io/part-of: luke
destination:
  namespace: prod-luke
  server: 'https://6C23DB44986B53F74BB0E6536985F1A4.gr7.ap-northeast-1.eks.amazonaws.com'
revision: saku-main
project: saku-prod
valueFiles:
  - values/saku/values-prod.yaml

publicAPI:
  enabled: true
  imageTag: "1.20.08053"

internalAPI:
  enabled: true
  imageTag: "1.20.08052"

jobManager:
  enabled: true
  imageTag: "1.20.08051"

decider:
  enabled: true
  imageTag: "1.20.0512"

deciderPriority:
  enabled: true
  imageTag: "1.20.0512"
  valueFiles:
    - values/saku/values-prod-priority.yaml

deciderPremium:
  enabled: true
  imageTag: "1.20.0512"
  valueFiles:
    - values/saku/values-prod-premium.yaml 

encoder:
  enabled: true
  imageTag: "1.20.08058"

encoderPriority:
  enabled: true
  imageTag: "1.20.08058"
  valueFiles:
    - values/saku/values-prod-priority.yaml

encoderPremium:
  enabled: true
  imageTag: "1.20.08058"
  valueFiles:
    - values/saku/values-prod-premium.yaml