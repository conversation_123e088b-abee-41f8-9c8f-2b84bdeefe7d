labels:
  app.kubernetes.io/name: luke
destination:
  namespace: luke
  server: https://kubernetes.default.svc
project: luke
soruce:
  repoURL: https://gitlab.kkinternal.com/kkstream/jcl/jcl-infrastructure/helm-charts/luke-k8s.git

revision: HEAD
valueFiles: []

publicAPI:
  enabled: false
  imageTag: false
  valueFiles: []

internalAPI:
  enabled: false
  imageTag: false
  valueFiles: []

jobManager:
  enabled: false
  imageTag: false
  valueFiles: []

decider:
  enabled: false
  imageTag: false
  valueFiles: []

deciderPriority:
  enabled: false
  imageTag: false
  valueFiles: []

deciderPremium:
  enabled: false
  imageTag: false
  valueFiles: []

encoder:
  enabled: false
  imageTag: false
  valueFiles: []

encoderPriority:
  enabled: false
  imageTag: false
  valueFiles: []

encoderPremium:
  enabled: false
  imageTag: false
  valueFiles: []

operationBot:
  enabled: false
  imageTag: false
  valueFiles: []