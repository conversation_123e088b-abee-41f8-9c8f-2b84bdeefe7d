enabled: true

nodeSelector:
  service: luke
  queue: normal

affinity:
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 1
        preference:
          matchExpressions:
          - key: priority
            operator: In
            values:
            - primary

tolerations:
  - effect: NoSchedule
    operator: Equal
    key: luke
    value: "true"

configMapEnv:
  S3_BUCKET_DEPLOY: kks-prod-saku-theater
  S3_BUCKET_WORKING: kks-prod-saku-studio
  SLACK_WEBHOOK_URL: *****************************************************************************
  SWF_DOMAIN: saku-prod-encoding

secret:
  awsData:
    credentials:
      enabled: true
      AWS_ACCESS_KEY_ID: ********************
      AWS_SECRET_ACCESS_KEY: wHINkkIQTF7yrcs7z6bJL9dUwbbFCQ7gcmoJQi+Z