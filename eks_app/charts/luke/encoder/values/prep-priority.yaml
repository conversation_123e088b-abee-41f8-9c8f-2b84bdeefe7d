enabled: true

labels:
  name: encoder-priority

nodeSelector:
  service: luke
  queue: priority

affinity:
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 1
        preference:
          matchExpressions:
          - key: priority
            operator: In
            values:
            - primary

tolerations:
  - effect: NoSchedule
    operator: Equal
    key: luke
    value: "true"

configMapEnv:
  S3_BUCKET_DEPLOY: kks-prep-saku-theater
  S3_BUCKET_WORKING: kks-prep-saku-studio
  SLACK_WEBHOOK_URL: *****************************************************************************
  SWF_DOMAIN: saku2-stag-encoding-priority

secret:
  awsData:
    credentials:
      enabled: true
      AWS_ACCESS_KEY_ID: ********************
      AWS_SECRET_ACCESS_KEY: sC/yXOXm+vMQU14UCk0tgqoGwhe9jOtIQlwc/ofs