enabled: true

image:
  tag: 1.25.0305

labels:
  name: encoder-premium

resources:
  deploy:
    limits:
      cpu: "36"
    requests:
      cpu: "2.1"
  encrypt:
    limits:
      cpu: "36"
    requests:
      cpu: "2.1"
  inspect:
    limits:
      cpu: "36"
    requests:
      cpu: "7"
  package:
    limits:
      cpu: "36"
    requests:
      cpu: "2.1"
  system:
    limits:
      cpu: "36"
    requests:
      cpu: "0.1"
  transcode:
    limits:
      cpu: "36"
    requests:
      cpu: "7"
  transcode4k:
    limits:
      cpu: "36"
    requests:
      cpu: "11.8"

nodeSelector:
  service: luke
  queue: premium

affinity:
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 1
        preference:
          matchExpressions:
            - key: priority
              operator: In
              values:
                - primary

tolerations:
  - effect: NoSchedule
    operator: Equal
    key: luke
    value: "true"

configMapEnv:
  S3_BUCKET_DEPLOY: kks-stag-saku-theater
  S3_BUCKET_WORKING: kks-stag-saku-studio
  SLACK_WEBHOOK_URL: *****************************************************************************
  SWF_DOMAIN: saku-stag-encoding-premium
