enabled: true

labels:
  name: encoder-priority

image:
  tag: 1.25.0305

nodeSelector:
  service: luke
  queue: priority

affinity:
  nodeAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 1
        preference:
          matchExpressions:
            - key: priority
              operator: In
              values:
                - primary

tolerations:
  - effect: NoSchedule
    operator: Equal
    key: luke
    value: "true"

configMapEnv:
  S3_BUCKET_DEPLOY: kks-qa-saku-theater
  S3_BUCKET_WORKING: kks-qa-saku-studio
  SLACK_WEBHOOK_URL: *****************************************************************************
  SWF_DOMAIN: saku-qa-encoding-priority

secret:
  awsData:
    credentials:
      enabled: true
      AWS_ACCESS_KEY_ID: ********************
      AWS_SECRET_ACCESS_KEY: uZOY9zIvZlDWo32/rJ536HvBa/BpQEiSwtQutDLU
