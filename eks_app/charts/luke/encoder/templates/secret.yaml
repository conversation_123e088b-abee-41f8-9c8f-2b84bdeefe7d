{{- if .Values.enabled -}}
{{- if .Values.secret.awsData.credentials.enabled -}}
{{ $fullname := include "luke-encoder.fullname" . }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $fullname }}-aws-config
type: Opaque
{{- with .Values.secret.awsData.credentials }}
data:
  AWS_ACCESS_KEY_ID: {{ .AWS_ACCESS_KEY_ID | b64enc | quote }}
  AWS_SECRET_ACCESS_KEY: {{ .AWS_SECRET_ACCESS_KEY | b64enc | quote }}
{{- end }}
{{- end }}
{{- end }}