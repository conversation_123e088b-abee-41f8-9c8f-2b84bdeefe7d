{{- if .Values.enabled }}
{{- $fullname := include "luke-encoder.fullname" . }}
{{- $encodingWorkerNamespace := .Release.Namespace }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullname }}-config
  labels:
    {{- include "luke-encoder.labels" . | nindent 4 }}
data:
  {{- range $key, $val := .Values.configMapEnv }}
    {{- if $val }}
      {{ $key }}: {{ tpl $val $ | quote }}
    {{- else }}
      {{ $key }}: ""
    {{- end }}
  {{- end }}

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullname }}
data:
  inspect-pod.tpl: |
    apiVersion: v1
    kind: Pod
    metadata:
      name: {{ $fullname }}-inspect-job-${ID}-${HASH}
      namespace: {{ $encodingWorkerNamespace }}
      {{- with .Values.labels }}
      labels:
        {{- toYaml . | nindent 8 -}}
      {{- end }}
    spec:
      restartPolicy: {{ .Values.restartPolicy }}
      containers:
        - name: luke-worker
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- with .Values.resources.inspect }}
          resources:
            {{- toYaml . | nindent 12 -}}
          {{- end }}
          envFrom:
            - configMapRef:
                name: {{ $fullname }}-config
            {{- if .Values.secret.awsData.credentials.enabled }}
            - secretRef:
                name: {{ $fullname }}-aws-config
            {{- end }}
          env:
            - name: TASK_LIST
              value: "inspect"
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  transcode-pod.tpl: |
    apiVersion: v1
    kind: Pod
    metadata:
      name: {{ $fullname }}-transcode-job-${ID}-${HASH}
      namespace: {{ $encodingWorkerNamespace }}
      {{- with .Values.labels }}
      labels:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      restartPolicy: {{ .Values.restartPolicy }}
      containers:
        - name: luke-worker
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- with .Values.resources.transcode }}
          resources:
            {{- toYaml . | nindent 12 -}}
          {{- end }}
          envFrom:
            - configMapRef:
                name: {{ $fullname }}-config
            {{- if .Values.secret.awsData.credentials.enabled }}
            - secretRef:
                name: {{ $fullname }}-aws-config
            {{- end }}
          env:
            - name: TASK_LIST
              value: "transcode"
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  transcode4k-pod.tpl: |
    apiVersion: v1
    kind: Pod
    metadata:
      name: {{ $fullname }}-transcode4k-job-${ID}-${HASH}
      namespace: {{ $encodingWorkerNamespace }}
      {{- with .Values.labels }}
      labels:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      restartPolicy: {{ .Values.restartPolicy }}
      containers:
        - name: luke-worker
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- with .Values.resources.transcode4k }}
          resources:
            {{- toYaml . | nindent 12 -}}
          {{- end }}
          envFrom:
            - configMapRef:
                name: {{ $fullname }}-config
            {{- if .Values.secret.awsData.credentials.enabled }}
            - secretRef:
                name: {{ $fullname }}-aws-config
            {{- end }}
          env:
            - name: TASK_LIST
              value: "transcode4k"
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  encrypt-pod.tpl: |
    apiVersion: v1
    kind: Pod
    metadata:
      name: {{ $fullname }}-encrypt-job-${ID}-${HASH}
      namespace: {{ $encodingWorkerNamespace }}
      {{- with .Values.labels }}
      labels:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      restartPolicy: {{ .Values.restartPolicy }}
      containers:
        - name: luke-worker
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- with .Values.resources.encrypt }}
          resources:
            {{- toYaml . | nindent 12 -}}
          {{- end }}
          envFrom:
            - configMapRef:
                name: {{ $fullname }}-config
            {{- if .Values.secret.awsData.credentials.enabled }}
            - secretRef:
                name: {{ $fullname }}-aws-config
            {{- end }}
          env:
            - name: TASK_LIST
              value: "encrypt"
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  package-pod.tpl: |
    apiVersion: v1
    kind: Pod
    metadata:
      name: {{ $fullname }}-package-job-${ID}-${HASH}
      namespace: {{ $encodingWorkerNamespace }}
      {{- with .Values.labels }}
      labels:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      restartPolicy: {{ .Values.restartPolicy }}
      containers:
        - name: luke-worker
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- with .Values.resources.package }}
          resources:
            {{- toYaml . | nindent 12 -}}
          {{- end }}
          envFrom:
            - configMapRef:
                name: {{ $fullname }}-config
            {{- if .Values.secret.awsData.credentials.enabled }}
            - secretRef:
                name: {{ $fullname }}-aws-config
            {{- end }}
          env:
            - name: TASK_LIST
              value: "package"
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  deploy-pod.tpl: |
    apiVersion: v1
    kind: Pod
    metadata:
      name: {{ $fullname }}-deploy-job-${ID}-${HASH}
      namespace: {{ $encodingWorkerNamespace }}
      {{- with .Values.labels }}
      labels:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      restartPolicy: {{ .Values.restartPolicy }}
      containers:
        - name: luke-worker
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- with .Values.resources.deploy }}
          resources:
            {{- toYaml . | nindent 12 -}}
          {{- end }}
          envFrom:
            - configMapRef:
                name: {{ $fullname }}-config
            {{- if .Values.secret.awsData.credentials.enabled }}
            - secretRef:
                name: {{ $fullname }}-aws-config
            {{- end }}
          env:
            - name: TASK_LIST
              value: "deploy"
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  system-pod.tpl: |
    apiVersion: v1
    kind: Pod
    metadata:
      name: {{ $fullname }}-system-job-${ID}-${HASH}
      namespace: {{ $encodingWorkerNamespace }}
      {{- with .Values.labels }}
      labels:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      restartPolicy: {{ .Values.restartPolicy }}
      containers:
        - name: luke-worker
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- with .Values.resources.system }}
          resources:
            {{- toYaml . | nindent 12 -}}
          {{- end }}
          envFrom:
            - configMapRef:
                name: {{ $fullname }}-config
            {{- if .Values.secret.awsData.credentials.enabled }}
            - secretRef:
                name: {{ $fullname }}-aws-config
            {{- end }}
          env:
            - name: TASK_LIST
              value: "system"
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
