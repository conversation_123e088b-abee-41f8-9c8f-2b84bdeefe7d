# Default values for sample.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true

image:
  repository: ************.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-luke-encoder
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

restartPolicy: OnFailure

labels:
  name: encoder

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {
    eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/saku/stage/luke/saku-stage-luke-irsa-role"
  }
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

resources:
  inspect:
    requests:
      cpu: "2.1"
    limits:
      cpu: "36"
  transcode:
    requests:
      cpu: "2.3"
    limits:
      cpu: "36"
  transcode4k:
    requests:
      cpu: "11.8"
    limits:
      cpu: "36"
  encrypt:
    requests:
      cpu: "2.1"
    limits:
      cpu: "36"
  package:
    requests:
      cpu: "2.1"
    limits:
      cpu: "36"
  deploy:
    requests:
      cpu: "2.1"
    limits:
      cpu: "36"
  system:
    requests:
      cpu: "0.1"
    limits:
      cpu: "36"

nodeSelector: {}

tolerations: []

affinity: {}

secret:
  awsData:
    credentials:
      enabled: false
      AWS_ACCESS_KEY_ID: ""
      AWS_SECRET_ACCESS_KEY: ""

configMapEnv:
  LUKE_CALLBACK_URL: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/encoding_reports"
  LUKE_ENCODING_SOURCE_URL: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/encoding_source"
  LUKE_GET_SUBTITLE_URLS_URL: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/get_subtitle_urls"
  LUKE_REPORT_ERROR_URL: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/report_error"
  LUKE_ENCODING_MANAGER_URL: "http://{{ .Release.Namespace }}-job-manager.{{ .Release.Namespace }}/api/v1/jobs"

  AES128_SYSTEM_ID: ce7acac260e0f1fcc2d343b6fcb9902a
  CMD_EXECUTION_TIMEOUT: "86400"
  CMD_MAX_RETRY: "3"
  CMD_WAITING_TIMEOUT: "2592000"
  CMS_API_HOSTNAME: http://wtf/
  DUMMY_KEY: da332ad81ee6d07ee058c286a0b35d93
  DUMMY_KEY_ID: 52d042fa8ed41f7f3d1f5721cc192022
  JOB_EXECUTION_TIMEOUT: "345600"
  JOB_TASK_EXCHANGE_RATE: "1000"
  JOB_WAITING_TIMEOUT: "2592000"
  NATALIE_WORKER_HEARTBEAT_INTERVAL: "300"
  NATALIE_WORKER_MIN_ROOT_STORAGE: "1"
  NATALIE_WORKER_MIN_WORKABLE_STORAGE: "1"
  NATALIE_WORKER_RESULT_CMD_MAX_LENGTH: "99999999"
  NATALIE_WORKER_ROOT_DIRECTORY: /
  NATALIE_WORKER_WORKING_DIRECTORY: /tmp
  PATH_MP4EXTRACT: mp4extract
  PLAYREADY_LICENSE_URL: http://drm.kkstream.tech/rightsmanager.asmx
  PLAYREADY_LOGIN_URL: http://drm.kkstream.tech/rightsmanager.asmx
  PLAYREADY_SYSTEM_ID: 9a04f07998404286ab92e65be0885f95
  PLAYREADY_VP_SERVICE_ID: 25ce1653701049b2ab0396b46dbbcda8
  S3_BUCKET_DEPLOY: jcl-test-theater #change
  S3_BUCKET_WORKING: jcl-test-studio #change
  SLACK_WEBHOOK_URL: ***************************************************************************** #change
  START_TASK_MAX_RETRY: "5"
  SWF_ACTIVITY_HEARTBEAT_INTERVAL: "900"
  SWF_ACTIVITY_HEARTBEAT_MAX_RETRY: "3"
  SWF_ACTIVITY_HEARTBEAT_RETRY_INTERVAL: "10"
  SWF_ACTIVITY_HEARTBEAT_TIMEOUT: "3600"
  SWF_ACTIVITY_NAME: Cmd
  SWF_ACTIVITY_TASK_LIST: natalie
  SWF_ACTIVITY_VERSION: "0.1"
  SWF_DOMAIN: jcl-test-encoding #change
  SWF_JOB_WORKFLOW_NAME: Job
  SWF_JOB_WORKFLOW_VERSION: "0.1"
  SWF_MAX_INPUT_DATA_SIZE: "32000"
  SWF_MAX_REASON_SIZE: "256"
  SWF_MAX_RETURN_DATA_SIZE: "32000"
  SWF_REGION: ap-northeast-1
  SWF_REQUEST_MAX_RETRY: "3"
  SWF_TASK_WORKFLOW_NAME: Task
  SWF_TASK_WORKFLOW_VERSION: "0.1"
  SWF_WORKFLOW_CHILD_POLICY: REQUEST_CANCEL
  SWF_WORKFLOW_EXECUTION_TIMEOUT: "60"
  SWF_WORKFLOW_TASK_LIST: natalie
  TASK_MAX_RETRY: "0"
  TMP_DIR2: /tmp
  WIDEVINE_SYSTEM_ID: edef8ba979d64acea3c827dcd51d21ed
  # P1:saku-test-encoding-premium;P2:saku-test-encoding-priority
  # by project
  SWF_DOMAIN_MAPPING: ""