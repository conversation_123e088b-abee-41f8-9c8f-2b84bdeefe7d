{{- if .Values.enableTest -}}
apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "luke-public-api.fullname" . }}-test-connection"
  labels:
    {{- include "luke-public-api.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "luke-public-api.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
{{- end }}
