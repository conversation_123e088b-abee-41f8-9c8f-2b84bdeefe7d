{{- if .Values.enabled }}
{{- $fullname := include "luke-public-api.fullname" . -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "luke-public-api.fullname" . }}
  labels:
    {{- include "luke-public-api.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "luke-public-api.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        {{- if .Values.prometheus_metrics.enabled }}
        prometheus.io/scrape: "{{ .Values.prometheus_metrics.enabled }}"
        prometheus.io/port: "{{ .Values.prometheus_metrics.port }}"
        {{- end }}
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
      labels:
        {{- include "luke-public-api.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- $volumes := len .Values.volumes -}}
      {{- if gt $volumes 0 }}
      volumes:
        {{- toYaml .Values.volumes | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "luke-public-api.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      initContainers:
        - name: nginx-tuning
          imagePullPolicy: IfNotPresent
          securityContext:
            privileged: true
          image: docker.io/busybox
          command:
            - sh
            - -c
          args:
          - |
            sysctl -w fs.file-max=2097152 kernel.sysrq=0 kernel.core_uses_pid=1 kernel.msgmnb=65536 kernel.msgmax=65536 kernel.shmmax=*********** kernel.shmall=**********
            sysctl -w vm.swappiness=10 vm.dirty_ratio=40 vm.dirty_background_ratio=10 net.core.somaxconn=65535
            sysctl -w net.ipv4.conf.default.rp_filter=1 net.ipv4.conf.default.accept_source_route=0 net.ipv4.ip_forward=0 net.ipv4.ip_local_port_range="2000 65535"
            ulimit -l unlimited
            sleep 5
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          envFrom:
            - configMapRef:
                name: {{ $fullname }}-env
          livenessProbe:
            httpGet:
              path: /healthz
              port: http
            initialDelaySeconds: 60
            periodSeconds: 60
          readinessProbe:
            httpGet:
              path: /api/v1/healthz
              port: http
            initialDelaySeconds: 10
            periodSeconds: 30
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- $volumeMounts := len .Values.volumeMounts -}}
          {{- if gt $volumeMounts 0 }}
          volumeMounts:
            {{- toYaml .Values.volumeMounts | nindent 12 }}
          {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}

{{- end }}
