image:
  tag: 1.25.0103

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 50

resources:
  limits:
    cpu: 250m
    memory: 350Mi
  requests:
    cpu: 200m
    memory: 300Mi

ingress:
  enabled: true
  className: "alb"
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /healthz
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
    alb.ingress.kubernetes.io/scheme: "internet-facing"
    alb.ingress.kubernetes.io/target-type: "ip"
    external-dns.alpha.kubernetes.io/aws-weight: "100"
    external-dns.alpha.kubernetes.io/set-identifier: "saku-stag-eks"
  hosts:
    - host: stag-luke-saku3.kkstream.tech
      paths:
        - path: /
          pathType: Prefix

configMapEnv:
  API_TOKEN_MAPPING: USy2KgghF6Bv4nSk:saku3
  SLACK_CHANNEL: luke-log-stag-api
