autoscaling:
  enabled: true
  minReplicas: 4
  maxReplicas: 10
  targetCPUUtilizationPercentage: 50

resources:
  limits:
   cpu: 250m
   memory: 350Mi
  requests:
   cpu: 200m
   memory: 300Mi

ingress:
  enabled: true
  className: "alb"
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /healthz
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
    alb.ingress.kubernetes.io/scheme: "internet-facing"
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/subnets: "saku-prod-public-1c,saku-prod-public-1a"
    external-dns.alpha.kubernetes.io/aws-weight: '100'
    external-dns.alpha.kubernetes.io/set-identifier: 'saku-prod-eks'
  hosts:
    - host: luke-saku.kkstream.tech
      paths:
        - path: /
          pathType: Prefix

configMapEnv:
  API_TOKEN_MAPPING: ED9FD8D4CE67A314F5F561DBAC6AD:saku
  SLACK_CHANNEL: ""
  BUCKET_MAPPING: telasa-prod-source-upload:telasa;saku-prod-telasa-shared-source-upload:st
