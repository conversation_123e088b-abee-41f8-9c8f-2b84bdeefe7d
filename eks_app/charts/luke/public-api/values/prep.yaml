autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 50

resources:
  limits:
   cpu: 250m
   memory: 350Mi
  requests:
   cpu: 200m
   memory: 300Mi

ingress:
  enabled: true
  className: "alb"
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /healthz
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
    alb.ingress.kubernetes.io/scheme: "internet-facing"
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/subnets: "saku-stag-public-1c,saku-stag-public-1a"
    external-dns.alpha.kubernetes.io/aws-weight: '100'
    external-dns.alpha.kubernetes.io/set-identifier: 'saku-prep-eks'
  hosts:
    - host: stag-luke-saku2.kkstream.tech
      paths:
        - path: /
          pathType: Prefix

configMapEnv:
  API_TOKEN_MAPPING: USy2KgghF6Bv4nSk:saku2
  SLACK_CHANNEL: luke-log-stag-api
