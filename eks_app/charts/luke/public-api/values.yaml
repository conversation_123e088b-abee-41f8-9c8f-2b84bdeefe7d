# Default values for luke-public-api.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
enabled: true
enableTest: false
replicaCount: 1

image:
  repository: ************.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-luke-public-api
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

volumes: {}

volumeMounts: {}

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  className: "alb"
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /healthz
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
    alb.ingress.kubernetes.io/scheme: "internet-facing"
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/ssl-policy: "ELBSecurityPolicy-TLS13-1-2-2021-06"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 500m
    memory: 500Mi
  requests:
    cpu: 200m
    memory: 200Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: topology.kubernetes.io/zone
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app.kubernetes.io/instance: "{{ .Release.Name }}"
  - maxSkew: 1
    topologyKey: kubernetes.io/hostname
    whenUnsatisfiable: DoNotSchedule
    labelSelector:
      matchLabels:
        app.kubernetes.io/instance: "{{ .Release.Name }}"

prometheus_metrics:
  enabled: true
  port: 80

configMapEnv:
  LUKE_INTERNAL_API_VENDOR: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/vendor"
  LUKE_INTERNAL_API_ENCODING_SOURCE_URL_V1_1: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1.1/encoding_source_url"
  LUKE_INTERNAL_API_ENCODING_STATUS: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/encoding_status"
  LUKE_INTERNAL_API_ENCODING_RESULT: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/encoding_result"
  LUKE_INTERNAL_API_ENCODING_RESULT_THUMBNAIL: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/encoding_result_thumbnail"
  LUKE_INTERNAL_API_ENCODING_HISTORY: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/encoding_history"
  LUKE_INTERNAL_API_ENCODING_HISTORY_V1_1: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1.1/encoding_history"
  LUKE_INTERNAL_API_ENCODING_SOURCE_LIST: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/encoding_source_list"
  LUKE_INTERNAL_API_ENCODING_SOURCE_LIST_V1_1: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1.1/encoding_source_list"
  LUKE_INTERNAL_API_TRIGGER_ENCODING_FLOW: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/trigger_encoding_flow"
  LUKE_INTERNAL_API_DELAYED_ENCODING_JOBS: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/delayed_encoding_jobs"
  LUKE_INTERNAL_API_DELAYED_WAITING_JOBS: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/delayed_waiting_jobs"
  LUKE_INTERNAL_API_ENCODING_JOB: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/encoding_job"
  LUKE_API_PROGRESS_CALLBACK: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/encoding_reports"
  LUKE_API_IS_READY_TO_PROCESS: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/is_ready_to_process"
  LUKE_REPORT_ERROR_URL: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/report_error"
  LUKE_API_VALIDATE_NAMING_RULE: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/validate_naming_rule"
  LUKE_ENCODING_MANAGER_HOST: "{{ .Release.Namespace }}-job-manager.{{ .Release.Namespace }}"
  BUCKET_MAPPING: ""

pdb:
  enabled: true
  maxUnavailable: 0
