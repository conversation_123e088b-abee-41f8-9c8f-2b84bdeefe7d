{{- if .Values.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "luke-internal-api.fullname" . }}
  labels:
    {{- include "luke-internal-api.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "luke-internal-api.selectorLabels" . | nindent 4 }}
{{- end }}
