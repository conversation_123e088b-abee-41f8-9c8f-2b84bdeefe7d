{{- if .Values.enableTest -}}
apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "luke-internal-api.fullname" . }}-test-connection"
  labels:
    {{- include "luke-internal-api.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "luke-internal-api.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
{{- end }}
