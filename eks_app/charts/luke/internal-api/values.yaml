# Default values for luke-internal-api.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
enabled: true
enableTest: false
replicaCount: 1

image:
  repository: ************.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-luke-internal-api
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

volumes: {}

volumeMounts: {}

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: false
  className: ""
  annotations:
    {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 500m
    memory: 500Mi
  requests:
    cpu: 200m
    memory: 200Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: topology.kubernetes.io/zone
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app.kubernetes.io/instance:  "{{ .Release.Name }}"
  - maxSkew: 1
    topologyKey: kubernetes.io/hostname
    whenUnsatisfiable: DoNotSchedule
    labelSelector:
      matchLabels:
        app.kubernetes.io/instance: "{{ .Release.Name }}"

configMapEnv:
  LUKE_INTERNAL_API_VENDOR: "htt://{{ .Release.Name }}.{{ .Release.Namespace }}"
  LUKE_INTERNAL_API_ENCODING_SOURCE_URL: "htt://{{ .Release.Name }}.{{ .Release.Namespace }}/api/v1/encoding_source_url"
  LUKE_INTERNAL_API_ENCODING_STATUS: "htt://{{ .Release.Name }}.{{ .Release.Namespace }}/api/v1/encoding_status"
  LUKE_ENCODING_MANAGER_HOST: "{{ .Release.Namespace }}-job-manager.{{ .Release.Namespace }}"

secretEnv:

prometheus_metrics:
  enabled: true
  port: 80

pdb:
  enabled: true
  maxUnavailable: 0
