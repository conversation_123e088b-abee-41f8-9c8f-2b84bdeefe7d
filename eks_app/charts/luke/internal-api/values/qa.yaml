image:
  tag: 1.25.0305

resources:
  limits:
    cpu: 350m
    memory: 500Mi
  requests:
    cpu: 300m
    memory: 400Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 50

affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - podAffinityTerm:
          labelSelector:
            matchExpressions:
              - key: service
                operator: In
                values:
                  - general
          topologyKey: topology.kubernetes.io/zone
        weight: 100
      - podAffinityTerm:
          labelSelector:
            matchExpressions:
              - key: service
                operator: In
                values:
                  - general
          topologyKey: kubernetes.io/hostname
        weight: 99

configMapEnv:
  SLACK_CHANNEL: luke-log-stag-api
  ENCODING_SLACK_CHANNEL: jcl-log-stag-encoding

secretEnv:
  DB_ENDPOINT: "saku-stag-db.cluster-ci4jygjfmibd.ap-northeast-1.rds.amazonaws.com"
  DB_API: "jcl"
  DB_USERNAME: "admin"
  DB_PASSWORD: "Passw0rd"
