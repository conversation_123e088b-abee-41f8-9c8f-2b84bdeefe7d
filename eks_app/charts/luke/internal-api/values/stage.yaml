# Default values for luke-internal-api.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
enabled: true
enableTest: false
replicaCount: 1

image:
  repository: ************.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-luke-internal-api
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: 1.25.0305

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

volumes: {}

volumeMounts: {}

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: false

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 350m
    memory: 500Mi
  requests:
    cpu: 300m
    memory: 400Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

configMapEnv:
  SLACK_CHANNEL: luke-log-stag-api
  ENCODING_SLACK_CHANNEL: jcl-log-stag-encoding

secretEnv:
  DB_ENDPOINT: "saku-stag-db.cluster-ci4jygjfmibd.ap-northeast-1.rds.amazonaws.com"
  DB_API: "jcl"
  DB_USERNAME: "admin"
  DB_PASSWORD: "Passw0rd"
