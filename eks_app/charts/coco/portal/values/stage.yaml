# Default values for sample.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true
enableTest: false
replicaCount: 1

image:
  repository: ************.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-coco-portal
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  # The image.tag overrides argocd application imageTag in meta/values-<env>.yaml.
  # Overrides Top Down : argocd application imageTag <- chart appVersion <- containers image.tag
  tag: "1.21.0625"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  className: alb
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: "/api/healthz"
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
    alb.ingress.kubernetes.io/scheme: "internet-facing"
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/group.name: "saku-stage-coco"
    alb.ingress.kubernetes.io/group.order: "2"
    alb.ingress.kubernetes.io/ssl-policy: "ELBSecurityPolicy-TLS13-1-2-2021-06"
    alb.ingress.kubernetes.io/security-groups: "saku-stage-coco-elb-sg"
    alb.ingress.kubernetes.io/manage-backend-security-group-rules: "true"
    external-dns.alpha.kubernetes.io/aws-weight: "100"
    external-dns.alpha.kubernetes.io/set-identifier: "saku-stage-eks"
  hosts:
    - host: stag-coco-saku-portal.kkstream.tech
      paths:
        - path: /
          pathType: Prefix
  tls: []

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 250m
    memory: 350Mi
  requests:
    cpu: 100m
    memory: 100Mi

container:
  port: 80
  liveness:
    path: /healthz
    port: 80
  readiness:
    path: /api/healthz
    port: 80

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 70

vpa:
  enabled: false
  resources:
    maxAllowed:
      cpu: 400m
      memory: 400Mi
    minAllowed:
      cpu: 100m
      memory: 100Mi

nodeSelector: {}

tolerations: []

affinity: {}

configMapEnv:
  API_URL_INTERNAL: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/"
  API_URL_MANAGER: "http://{{ .Release.Namespace }}-linear-tv-manager.{{ .Release.Namespace }}"
  ENV: stag
  URL_SLACK_WEBHOOK_O2: "*****************************************************************************"
