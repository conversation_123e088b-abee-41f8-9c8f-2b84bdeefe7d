{{- if .Values.enableTest }}
apiVersion: v1
kind: Pod
metadata:
  name: {{ include "coco-portal.fullname" . }}-test-connection
  labels:
    {{- include "coco-portal.labels" . | nindent 4 }}
  annotations:
    helm.sh/hook: test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "coco-portal.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
{{- end }}
