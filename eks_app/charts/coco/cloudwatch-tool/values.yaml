# Default values for coco-cloudwatch-tool.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true

replicaCount: 1

image:
  repository: "138046196805.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-coco-cloudwatch-tool"
  tag: "1.20.0309"
  pullPolicy: IfNotPresent

nameOverride: "cloudwatch-tool"
fullnameOverride: ""

strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 1

service: {}

schedule: "0 1 * * *"

concurrencyPolicy: "Forbid"

failedJobsHistoryLimit: 3

successfulJobsHistoryLimit: 1

restartPolicy: "OnFailure"

ingress: {}

resources:
  limits:
    cpu: 120m
    memory: 200Mi
  requests:
    cpu: 100m
    memory: 150Mi

tolerations: []

affinity: {}

horizontalPodAutoscaler:
  enabled: false

secret:
  awsConfig:
    enabled: true

configMapEnv:
  AWS_DEFAULT_PROFILE: ""
  API_URL_INTERNAL: ""
  API_URL_MANAGER: ""
  AWS_CONFIG_FILE: ""
  AWS_SHARED_CREDENTIALS_FILE: ""
  PROJECT_NAME: ""
  S3_BUCKECT_NAME: ""
  ENV: ""

# argocd bug,so disable topologySpreadConstraints ref: https://github.com/argoproj/argo-cd/issues/16400
# topologySpreadConstraints:
#   - maxSkew: 1
#     topologyKey: topology.kubernetes.io/zone
#     whenUnsatisfiable: ScheduleAnyway
#     labelSelector:
#       matchLabels:
#         app.kubernetes.io/instance:  "{{ .Release.Name }}"
#   - maxSkew: 1
#     topologyKey: kubernetes.io/hostname
#     whenUnsatisfiable: DoNotSchedule
#     labelSelector:
#       matchLabels:
#         app.kubernetes.io/name: "{{ .Release.Name }}"
