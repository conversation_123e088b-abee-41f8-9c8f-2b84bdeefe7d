{{- if .Values.enabled -}}
{{ $fullname := include "coco-cloudwatch-tool.fullname" . }}
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ $fullname }}
  labels:
    app.kubernetes.io/name: {{ include "coco-cloudwatch-tool.name" . }}
    helm.sh/chart: {{ include "coco-cloudwatch-tool.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  schedule: {{ .Values.schedule | quote }}
  concurrencyPolicy: {{ .Values.concurrencyPolicy }}
  failedJobsHistoryLimit: {{ .Values.failedJobsHistoryLimit }}
  successfulJobsHistoryLimit: {{ .Values.successfulJobsHistoryLimit }}
  jobTemplate:
    spec:
      template:
        metadata:
          annotations:
            checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
          labels:
            app.kubernetes.io/name: {{ include "coco-cloudwatch-tool.name" . }}
            app.kubernetes.io/instance: {{ .Release.Name }}
        spec:
          restartPolicy: "{{ .Values.restartPolicy }}"
          containers:
            - name: "channel-active-time"
              image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
              imagePullPolicy: {{ .Values.image.pullPolicy }}
              command: ["/usr/local/bin/python", "/app/src/ChannelActiveTime.py"]
              resources:
                {{- toYaml .Values.resources | nindent 16 }}
              envFrom:
                - configMapRef:
                    name: {{ $fullname }}-env-config
          {{- if .Values.secret.awsConfig.enabled }}
              volumeMounts:
                - name: aws-config
                  mountPath: "/etc/aws"
          volumes:
            - name: aws-config
              secret:
                secretName: {{ $fullname }}-aws-config
          {{- end }}
          {{- with .Values.nodeSelector }}
          nodeSelector:
            {{- toYaml . | nindent 12 }}
          {{- end }}
        {{- with .Values.tolerations }}
          tolerations:
            {{- toYaml . | nindent 12 }}
        {{- end }}
        {{- with .Values.topologySpreadConstraints }}
        topologySpreadConstraints:
          {{- tpl (toYaml .) $ | nindent 8 }}
        {{- end }}
{{- end }}
