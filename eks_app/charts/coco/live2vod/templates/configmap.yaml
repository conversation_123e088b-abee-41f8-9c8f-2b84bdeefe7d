{{- if .Values.enabled }}
{{- $fullname := include "coco-live2vod.fullname" . -}}
{{- $live2vodWorkerNamespace := .Release.Namespace }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullname }}-tpl
  labels:
    {{- include "coco-live2vod.labels" . | nindent 4 }}
data:
  live2vod-worker.tpl: |
    apiVersion: v1
    kind: Pod
    metadata:
      name: {{ $fullname }}-${TAG}
      namespace: {{ $live2vodWorkerNamespace }}
      labels:
        app: {{ $fullname }}
    spec:
      restartPolicy: {{ .Values.restartPolicy }}
      containers:
        - name: coco-live2vod-worker
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default $.Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ${COMMAND_EXECUTE}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          envFrom:
          - configMapRef:
              name: {{ $fullname }}-env
          {{- if .Values.secret.aws.enabled }}
          volumeMounts:
            - name: aws-config
              mountPath: "/etc/aws"
          {{- end }}
      {{- if .Values.secret.aws.enabled }}
      volumes:
        - name: aws-config
          secret:
            secretName: {{ $fullname }}-aws-config
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullname }}-env
data:
  {{- range $key, $val := .Values.configMapEnv }}
    {{- if $val }}
      {{ $key }}: {{ $val | quote }}
    {{- else }}
      {{ $key }}: ""
    {{- end }}
  {{- end }}
{{- end }}
