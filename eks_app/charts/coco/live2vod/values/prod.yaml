# Default values for sample.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true

image:
  repository: ************.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-coco-live-to-vod
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  # The image.tag overrides argocd application imageTag in meta/values-<env>.yaml. 
  # Overrides Top Down : argocd application imageTag <- chart appVersion <- containers image.tag
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

parallelism:
completions:
activeDeadlineSeconds:
backoffLimit:

volumeClaimTemplate:
  storageRequests: 160Gi
  storageClassName: gp2

pollingInterval:
successfulJobsHistoryLimit:
failedJobsHistoryLimit:
minReplicaCount:
maxReplicaCount:

rollout:
  strategy: default
  propagationPolicy: background

scalingStrategy:
  strategy: default

triggers: []

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 1000m
    memory: 500Mi
    ephemeral-storage: 60Gi
  requests:
    cpu: 500m
    memory: 500Mi
    ephemeral-storage: 40Gi

nodeSelector:
  service: coco
  #purpose: "coco-live2vod-t3-medium-450g-nodes" #for old saku

tolerations:
  - key: "coco"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"
# for old saku
# tolerations:
#   - effect: NoSchedule
#     operator: Equal
#     key: dedicated
#     value: live2vod

affinity: {}

configMapEnv:
  ENV: prod
  AWS_DEFAULT_PROFILE: saku
  AWS_CONFIG_FILE: /etc/aws/config
  AWS_SHARED_CREDENTIALS_FILE: /etc/aws/credentials
  URL_SLACK_WEBHOOK: "*****************************************************************************"
  THREAD_NUMBER: "5"
secret: #for aws-config
  aws:
    enabled: true
