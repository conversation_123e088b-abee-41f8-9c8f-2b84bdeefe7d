# Default values for sample.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true

restartPolicy: OnFailure
image:
  repository: ************.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-coco-live-to-vod
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

parallelism:
completions:
activeDeadlineSeconds:
backoffLimit:

volumeClaimTemplate:
  storageRequests: 160Gi
  storageClassName: gp2

pollingInterval:
successfulJobsHistoryLimit:
failedJobsHistoryLimit:
minReplicaCount:
maxReplicaCount:

rollout:
  strategy: default
  propagationPolicy: background

scalingStrategy:
  strategy: default

triggers: []

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 500m
    memory: 500Mi
  requests:
    cpu: 200m
    memory: 200Mi

nodeSelector:
  service: coco

tolerations:
  - key: "coco"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"

affinity: {}

configMapEnv:
  ENV:
  SENTRY_DSN:
  SQS_URL_LIVE2VOD:
  THREAD_NUMBER:
  URL_SLACK_WEBHOOK:

secret: #for aws-config
  aws:
    enabled: false

# argocd bug,so disable topologySpreadConstraints ref: https://github.com/argoproj/argo-cd/issues/16400
# topologySpreadConstraints:
#   - maxSkew: 1
#     topologyKey: topology.kubernetes.io/zone
#     whenUnsatisfiable: ScheduleAnyway
#     labelSelector:
#       matchLabels:
#         app.kubernetes.io/instance:  "{{ .Release.Name }}"
#   - maxSkew: 1
#     topologyKey: kubernetes.io/hostname
#     whenUnsatisfiable: DoNotSchedule
#     labelSelector:
#       matchLabels:
#         app.kubernetes.io/name: "{{ .Release.Name }}"
