labels:
  app.kubernetes.io/name: coco #coco meta app is managed. #mapping to jcl-argocd-tenant managed coco.
destination:
  namespace: coco
  server: https://kubernetes.default.svc
project: coco
soruce:
  repoURL: https://gitlab.kkinternal.com/kkstream/jcl/jcl-infrastructure/helm-charts/coco-k8s.git

revision: HEAD
valueFiles: []

assetChecker:
#  revision: HEAD
  enabled: true
  imageTag: false
  valueFiles: []

catchupScheduler:
#  revision: HEAD
  enabled: true
  catchupHandler:
    imageTag: false
  statusHandler:
    imageTag: false
  valueFiles: []

cloudwatchTool:
#  revision: HEAD
  enabled: true
  imageTag: false
  valueFiles: []

internalApi:
#  revision: HEAD
  enabled: true
  imageTag: false
  valueFiles: []

linearAutomaticSwitch:
#  revision: HEAD
  enabled: true
  imageTag: false
  valueFiles: []

linearTvManager:
#  revision: HEAD
  enabled: true
  imageTag: false
  valueFiles: []

live2VodStatusChecker:
#  revision: HEAD
  enabled: true
  imageTag: false
  valueFiles: []

live2Vod:
#  revision: HEAD
  enabled: true
  imageTag: false
  valueFiles: []

portal:
#  revision: HEAD
  enabled: true
  imageTag: false
  valueFiles: []

thumbnail:
#  revision: HEAD
  enabled: true
  imageTag: false
  valueFiles: []
