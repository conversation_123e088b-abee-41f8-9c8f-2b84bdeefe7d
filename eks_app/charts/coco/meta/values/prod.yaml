labels:
  kkstream.io/project: saku
  kkstream.io/env: prod
  app.kubernetes.io/part-of: coco

destination:
  namespace: prod-coco
  server: "https://6C23DB44986B53F74BB0E6536985F1A4.gr7.ap-northeast-1.eks.amazonaws.com"
revision: saku-main
project: saku-prod

valueFiles: #any apps will read default value file for saku prod.
  - values/saku/values-prod.yaml #charts/<app>/values/saku/values-prod.yaml

assetChecker:
  imageTag: 1.24.0606

catchupScheduler:
  catchupHandler:
    imageTag: 1.24.0410
  statusHandler:
    imageTag: 1.24.0410

cloudwatchTool:
  enabled: false

internalApi:
  imageTag: 1.20.1021

linearAutomaticSwitch:
  imageTag: 1.24.0319

linearTvManager:
  imageTag: 1.24.06062

live2VodStatusChecker:
  imageTag: 1.19.1108

live2Vod:
  imageTag: 1.21.0401

portal:
  imageTag: 1.21.0625

thumbnail:
  imageTag: 1.21.0401
