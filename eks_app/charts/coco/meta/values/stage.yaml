labels:
  kkstream.io/project: saku
  kkstream.io/env: stage
  app.kubernetes.io/part-of: coco

destination:
  namespace: stage-coco
  server: "https://EEF78B478B346E257FE07D9DFC95165D.gr7.ap-northeast-1.eks.amazonaws.com"
revision: saku-main
project: saku-non-prod

valueFiles: #any apps will read default value file for saku stage.
  - values/saku/values-stage.yaml #charts/<app>/values/saku/values-stage.yaml

assetChecker:
  imageTag: 1.24.0606

catchupScheduler:
  catchupHandler:
    imageTag: 1.24.0319
  statusHandler:
    imageTag: 1.24.0319

cloudwatchTool:
  enabled: false

internalApi:
  imageTag: 1.20.1021

linearAutomaticSwitch:
  imageTag: 1.24.0319

linearTvManager:
  imageTag: 1.24.06062

live2VodStatusChecker:
  imageTag: 1.19.1108

live2Vod:
  imageTag: 1.21.0401

portal:
  imageTag: 1.21.0625

thumbnail:
  imageTag: 1.21.0401
