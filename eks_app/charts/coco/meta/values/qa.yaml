labels:
  kkstream.io/project: saku
  kkstream.io/env: qa
  app.kubernetes.io/part-of: coco

destination:
  namespace: qa-coco
  server: "https://EEF78B478B346E257FE07D9DFC95165D.gr7.ap-northeast-1.eks.amazonaws.com"
revision: saku-main
project: saku-non-prod

valueFiles: #any apps will read default value file for saku qa.
  - values/saku/values-qa.yaml #charts/<app>/values/saku/values-qa.yaml

assetChecker:
  imageTag: 1.24.0606

catchupScheduler:
  catchupHandler:
    imageTag: 1.24.0410
  statusHandler:
    imageTag: 1.24.0410

cloudwatchTool:
  enabled: false

internalApi:
  imageTag: 1.20.1021

linearAutomaticSwitch:
  enabled: false

linearTvManager:
  imageTag: 1.24.06062

live2VodStatusChecker:
  imageTag: 1.19.1108

live2Vod:
  imageTag: 1.21.0401

portal:
  imageTag: 1.21.0625

thumbnail:
  imageTag: 1.21.0401
