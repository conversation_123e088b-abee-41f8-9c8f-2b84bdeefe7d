{{- if .Values.portal.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-portal
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/portal
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.portal.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-portal
      {{- with .Values.portal.imageTag }}
      parameters:
        - name: image.tag
          value: {{ . | quote }}
      {{- end }}
      valueFiles:
        {{- if not .Values.portal.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else}}
          {{- range .Values.portal.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end}}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true

{{- end }}