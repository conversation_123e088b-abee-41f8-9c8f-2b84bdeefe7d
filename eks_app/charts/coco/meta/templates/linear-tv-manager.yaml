{{- if .Values.linearTvManager.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-linear-tv-manager
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/linear-tv-manager
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.linearTvManager.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-linear-tv-manager
      {{- with .Values.linearTvManager.imageTag }}
      parameters:
        - name: image.tag
          value: {{ . | quote }}
      {{- end }}
      valueFiles:
        {{- if not .Values.linearTvManager.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else}}
          {{- range .Values.linearTvManager.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end}}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true

{{- end }}