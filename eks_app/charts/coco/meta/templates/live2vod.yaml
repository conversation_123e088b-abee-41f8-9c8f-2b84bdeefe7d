{{- if .Values.live2Vod.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-live2vod
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/live2vod
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.live2Vod.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-live2vod
      {{- with .Values.live2Vod.imageTag }}
      parameters:
        - name: image.tag
          value: {{ . | quote }}
      {{- end }}
      valueFiles:
        {{- if not .Values.live2Vod.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else}}
          {{- range .Values.live2Vod.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end}}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true

{{- end }}