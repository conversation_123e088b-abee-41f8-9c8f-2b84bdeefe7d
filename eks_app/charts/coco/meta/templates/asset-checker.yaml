{{- if .Values.assetChecker.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-asset-checker
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/asset-checker
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.assetChecker.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-asset-checker
      {{- with .Values.assetChecker.imageTag }}
      parameters:
        - name: image.tag
          value: {{ . | quote }}
      {{- end }}
      valueFiles:
        {{- if not .Values.assetChecker.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else}}
          {{- range .Values.assetChecker.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end}}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
{{- end}}