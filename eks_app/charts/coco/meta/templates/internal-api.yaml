{{- if .Values.internalApi.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-internal-api
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/internal-api
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.internalApi.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-internal-api
      {{- with .Values.internalApi.imageTag }}
      parameters:
        - name: image.tag
          value: {{ . | quote }}
      {{- end }}
      valueFiles:
        {{- if not .Values.internalApi.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else}}
          {{- range .Values.internalApi.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end}}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true

{{- end }}