{{- if .Values.catchupScheduler.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-catchup-scheduler
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/catchup-scheduler
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.catchupScheduler.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-catchup-scheduler
      {{- with .Values.catchupScheduler }}
      parameters:
        - name: catchupHandler.image.tag
          value: {{ .catchupHandler.imageTag | quote }}
        - name: statusHandler.image.tag
          value: {{ .statusHandler.imageTag | quote }} 
      {{- end }}
      valueFiles:
        {{- if not .Values.catchupScheduler.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else}}
          {{- range .Values.catchupScheduler.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end}}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true

{{- end }}