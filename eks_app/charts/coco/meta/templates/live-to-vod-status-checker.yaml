{{- if .Values.live2VodStatusChecker.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-live-to-vod-status-checker
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/live-to-vod-status-checker
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.live2VodStatusChecker.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-live-to-vod-status-checker
      {{- with .Values.live2VodStatusChecker.imageTag }}
      parameters:
        - name: image.tag
          value: {{ . | quote }}
      {{- end }}
      valueFiles:
        {{- if not .Values.live2VodStatusChecker.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else}}
          {{- range .Values.live2VodStatusChecker.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end}}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true

{{- end }}