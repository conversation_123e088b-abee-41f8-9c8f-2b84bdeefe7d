{{- if .Values.cloudwatchTool.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-cloudwatch-tool
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/cloudwatch-tool
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.cloudwatchTool.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-cloudwatch-tool
      {{- with .Values.cloudwatchTool.imageTag }}
      parameters:
        - name: image.tag
          value: {{ . | quote }}
      {{- end }}
      valueFiles:
        {{- if not .Values.cloudwatchTool.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else}}
          {{- range .Values.cloudwatchTool.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end}}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true

{{- end }}