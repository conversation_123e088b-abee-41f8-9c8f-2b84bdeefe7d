{{- if .Values.thumbnail.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-thumbnail
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/thumbnail
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.thumbnail.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-thumbnail
      {{- with .Values.thumbnail.imageTag }}
      parameters:
        - name: image.tag
          value: {{ . | quote }}
      {{- end }}
      valueFiles:
        {{- if not .Values.thumbnail.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else}}
          {{- range .Values.thumbnail.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end}}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true

{{- end }}