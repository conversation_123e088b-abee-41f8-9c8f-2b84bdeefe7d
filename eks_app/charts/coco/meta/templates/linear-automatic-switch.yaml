{{- if .Values.linearAutomaticSwitch.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{ .Release.Name }}-linear-automatic-switch
  labels: {{ toYaml .Values.labels | nindent 4 }}
spec:
  destination: {{ toYaml .Values.destination | nindent 4 }}
  project: {{ .Values.project }}
  source:
    path: charts/linear-automatic-switch
    repoURL: {{ .Values.soruce.repoURL | quote }}
    targetRevision: {{ .Values.linearAutomaticSwitch.revision | default .Values.revision | quote }}
    helm:
      releaseName: {{ .Values.destination.namespace }}-linear-automatic-switch
      {{- with .Values.linearAutomaticSwitch.imageTag }}
      parameters:
        - name: image.tag
          value: {{ . | quote }}
      {{- end }}
      valueFiles:
        {{- if not .Values.linearAutomaticSwitch.valueFiles }}
          {{- range .Values.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- else}}
          {{- range .Values.linearAutomaticSwitch.valueFiles }}
          - {{ . | quote }}
          {{- end }}
        {{- end}}
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
{{- end }}