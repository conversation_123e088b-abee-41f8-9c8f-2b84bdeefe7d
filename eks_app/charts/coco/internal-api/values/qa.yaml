# Default values for sample.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true
enableTest: false
includeAwsCredential: true
replicaCount: 1

image:
  repository: ************.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-coco-internal-api
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  # The image.tag overrides argocd application imageTag in meta/values-<env>.yaml.
  # Overrides Top Down : argocd application imageTag <- chart appVersion <- containers image.tag
  tag: "1.20.1021"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 400m
    memory: 500Mi
  requests:
    cpu: 200m
    memory: 200Mi

container:
  port: 80
  liveness:
    path: /healthz
    port: 80
  readiness:
    path: /api/v1/healthz
    port: 80
  livenessInitialDelaySeconds: 10
  livenessPeriodSeconds: 60
  readinessInitialDelaySeconds: 10
  readinessPeriodSeconds: 30

volumes:
  - secret:
      #secretName: stage-coco-internal-api-aws-credential
      secretName: "{{ .Release.Name }}-aws-credential"
    name: aws-config

volumeMounts:
  - name: aws-config
    mountPath: /etc/aws

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 50
  targetMemoryUtilizationPercentage: 50

vpa:
  enabled: false
  resources:
    maxAllowed:
      cpu: 400m
      memory: 400Mi
    minAllowed:
      cpu: 100m
      memory: 100Mi

nodeSelector: {}

tolerations: []

affinity: {}

configMapEnv:
  AWS_CONFIG_FILE: /etc/aws/config
  AWS_DEFAULT_PROFILE: saku
  AWS_SHARED_CREDENTIALS_FILE: /etc/aws/credentials
  ENV: qa
  SLACK_CHANNEL: jcl-log-stag-coco

secretEnv:
  DB_API: "jcl"
  DB_ENDPOINT: "saku-stag-db.cluster-ci4jygjfmibd.ap-northeast-1.rds.amazonaws.com"
  DB_PASSWORD: "Passw0rd"
  DB_USERNAME: "admin"
