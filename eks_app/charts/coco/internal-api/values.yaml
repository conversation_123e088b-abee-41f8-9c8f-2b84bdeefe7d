# Default values for sample.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true
enableTest: false
includeAwsCredential: false
replicaCount: 1

image:
  repository: ************.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-coco-internal-api
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 400m
    memory: 500Mi
  requests:
    cpu: 200m
    memory: 200Mi

container:
  port: 80
  liveness:
    path: /healthz
    port: 80
  readiness:
    path: /api/v1/healthz
    port: 80

volumes:
  []
  # - secret:
  #     secretName: coco-internal-api-aws-credential
  #   name: aws-config

volumeMounts:
  []
  # - name: aws-config
  #   mountPath: /etc/aws

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 50
  targetMemoryUtilizationPercentage: 50

vpa:
  enabled: true
  resources:
    maxAllowed:
      cpu: 400m
      memory: 400Mi
    minAllowed:
      cpu: 100m
      memory: 100Mi

nodeSelector: {}

tolerations: []

affinity: {}

configMapEnv:
  # AWS_CONFIG_FILE:
  # AWS_DEFAULT_PROFILE:
  # AWS_SHARED_CREDENTIALS_FILE:
  ENV:
  SLACK_CHANNEL:

secretEnv:
  DB_API:
  DB_ENDPOINT:
  DB_PASSWORD:
  DB_USERNAME:

# argocd bug,so disable topologySpreadConstraints ref: https://github.com/argoproj/argo-cd/issues/16400
# topologySpreadConstraints:
#   - maxSkew: 1
#     topologyKey: topology.kubernetes.io/zone
#     whenUnsatisfiable: ScheduleAnyway
#     labelSelector:
#       matchLabels:
#         app.kubernetes.io/instance:  "{{ .Release.Name }}"
#   - maxSkew: 1
#     topologyKey: kubernetes.io/hostname
#     whenUnsatisfiable: DoNotSchedule
#     labelSelector:
#       matchLabels:
#         app.kubernetes.io/name: "{{ .Release.Name }}"

pdb:
  enabled: true
  maxUnavailable: 0
