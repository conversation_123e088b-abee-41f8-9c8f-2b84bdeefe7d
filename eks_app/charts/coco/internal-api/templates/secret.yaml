{{- if .Values.enabled }}
{{- $fullname := include "coco-internal-api.fullname" . -}}
{{- if .Values.includeAwsCredential }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $fullname }}-aws-credential
  labels:
    {{- include "coco-internal-api.labels" . | nindent 4 }}
type: Opaque
data:
  {{- $files := .Files }}
  {{- range tuple "secret/config" "secret/credentials" }}
  {{ base (.) }}: |-
    {{ $files.Get . | b64enc }}
  {{- end }}
{{- end }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ $fullname }}-env
  labels:
    {{- include "coco-internal-api.labels" . | nindent 4 }}
type: Opaque
data:
  {{- range $key, $val := .Values.secretEnv }}
    {{- if $val }}
      {{ $key }}: {{ $val | b64enc }}
    {{- else }}
      {{ $key }}: ""
    {{- end }}
  {{- end }}
{{- end }}
