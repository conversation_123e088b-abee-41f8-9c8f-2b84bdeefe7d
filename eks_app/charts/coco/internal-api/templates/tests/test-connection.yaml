{{- if .Values.enableTest }}
apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "coco-catchup-scheduler.fullname" . }}-test-connection"
  labels:
    {{- include "coco-catchup-scheduler.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "coco-catchup-scheduler.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
{{- end }}
