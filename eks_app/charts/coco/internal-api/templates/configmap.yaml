{{- if .Values.enabled }}
{{- $fullname := include "coco-internal-api.fullname" . -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullname }}-env
  labels:
    {{- include "coco-internal-api.labels" . | nindent 4 }}
data:
  {{- range $key, $val := .Values.configMapEnv }}
    {{- if $val }}
      {{ $key }}: {{ $val | quote }}
    {{- else }}
      {{ $key }}: ""
    {{- end }}
  {{- end }}
{{- end }}
