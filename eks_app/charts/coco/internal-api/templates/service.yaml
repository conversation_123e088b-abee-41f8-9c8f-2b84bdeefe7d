{{- if .Values.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "coco-internal-api.fullname" . }}
  labels:
    {{- include "coco-internal-api.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "coco-internal-api.selectorLabels" . | nindent 4 }}
{{- end }}
