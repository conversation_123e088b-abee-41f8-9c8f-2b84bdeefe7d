{{- if .Values.enabled }}
{{- $fullname := include "coco-thumbnail.fullname" . -}}
{{- $thumbnailWorkerNamespace := .Release.Namespace }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullname }}-tpl
  labels:
    {{- include "coco-thumbnail.labels" . | nindent 4 }}
data:
  thumbnail-worker.tpl: |
    apiVersion: v1
    kind: Pod
    metadata:
      name: {{ $fullname }}-${TAG}
      namespace: {{ $thumbnailWorkerNamespace }} 
      labels:
        app: {{ $fullname }}
    spec:
      restartPolicy: OnFailure
      serviceAccountName: {{ $fullname }}
      containers:
        - name: {{ $fullname }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default $.Chart.AppVersion }}"
          imagePullPolicy: IfNotPresent
          command: ${COMMAND_EXECUTE}
          resources:
            limits:
              cpu: 500m
              memory: 500Mi
            requests:
              cpu: 250m
              memory: 250Mi
          envFrom:
          - configMapRef:
              name: {{ $fullname }}-env
          volumeMounts:
            - mountPath: /cache
              name: cache-volume
            - name: aws-config
              mountPath: "/etc/aws"
      volumes:
        - name: cache-volume
          emptyDir: {}
        - secret:
            secretName: "{{ $fullname }}-aws-credential"
          name: aws-config
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullname }}-env
data:
  {{- range $key, $val := .Values.configMapEnv }}
    {{- if $val }}
      {{ $key }}: {{ $val | quote }}
    {{- else }}
      {{ $key }}: ""
    {{- end }}
  {{- end }}
{{- end }}