{{- if .Values.enabled }}
{{- $fullname := include "coco-thumbnail.fullname" . -}}
{{- if .Values.includeAwsCredential }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $fullname }}-aws-credential
  labels:
    {{- include "coco-thumbnail.labels" . | nindent 4 }}
type: Opaque
data:
  {{- $files := .Files }}
  {{- range tuple "secret/config" "secret/credentials" }}
  {{ base (.) }}: |-
    {{ $files.Get . | b64enc }}
  {{- end }}
{{- end }}
{{- end }}