# Default values for sample.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true
enableTest: false
includeAwsCredential: true
replicaCount: 1

image:
  repository: ************.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-coco-thumbnail
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  # The image.tag overrides argocd application imageTag in meta/values-<env>.yaml.
  # Overrides Top Down : argocd application imageTag <- chart appVersion <- containers image.tag
  tag: "1.21.0401"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

parallelism:
completions:
activeDeadlineSeconds:
backoffLimit:

volumeClaimTemplate:
  storageRequests: 160Gi
  storageClassName: gp2

pollingInterval:
successfulJobsHistoryLimit:
failedJobsHistoryLimit:
minReplicaCount:
maxReplicaCount:

rollout:
  strategy: default
  propagationPolicy: background

scalingStrategy:
  strategy: default

triggers: []

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 500m
    memory: 500Mi
  requests:
    cpu: 200m
    memory: 200Mi

affinity: {}

configMapEnv:
  ENV: stag
  URL_SLACK_WEBHOOK: "*******************************************************************************"
  AWS_CONFIG_FILE: /etc/aws/config
  AWS_DEFAULT_PROFILE: saku
  AWS_SHARED_CREDENTIALS_FILE: /etc/aws/credentials
