apiVersion: v1
kind: Pod
metadata:
  name: coco-thumbnail-${TAG}
  namespace: coco
  labels:
    app: thumbnail
    service: coco
spec:
  restartPolicy: OnFailure
  serviceAccountName: coco-thumbnail
  containers:
    - name: coco-thumbnail
      image: ************.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-coco-thumbnail:IMAGE_TAG
      imagePullPolicy: IfNotPresent
      command: ${COMMAND_EXECUTE}
      resources:
        limits:
          cpu: 500m
          memory: 500Mi
        requests:
          cpu: 250m
          memory: 250Mi
      envFrom:
        - configMapRef:
            name: coco-thumbnail-env