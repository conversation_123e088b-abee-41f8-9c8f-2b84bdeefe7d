# Default values for jcl-coco-live-to-vod-status-checker-helm.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true

replicaCount: 1

image:
  repository: "138046196805.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-coco-live-to-vod-status-checker"
  tag: ""
  pullPolicy: IfNotPresent

nameOverride: "live-to-vod-status-checker"
fullnameOverride: ""

strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 1

service: {}

ingress: {}

resources:
  limits:
    cpu: 200m
    memory: 300Mi
  requests:
    cpu: 150m
    memory: 250Mi

# nodeSelector:
#   purpose: nodes

tolerations: []

# affinity:
#   nodeAffinity:
#     requiredDuringSchedulingIgnoredDuringExecution:
#       nodeSelectorTerms:
#       - matchExpressions:
#         - key: purpose
#           operator: In
#           values:
#           - nodes
#   podAntiAffinity:
#     preferredDuringSchedulingIgnoredDuringExecution:
#     - weight: 100
#       podAffinityTerm:
#         labelSelector:
#           matchExpressions:
#           - key: app.kubernetes.io/name
#             operator: In
#             values:
#             - live-to-vod-status-checker
#         topologyKey: kubernetes.io/hostname

horizontalPodAutoscaler:
  enabled: false

configMap:
  enabled: false
  defaultData: {}

configMapEnv:
  API_URL_INTERNAL: ""
  API_URL_MANAGER: ""
  CHANNEL_NAME: ""
  VENDER_TOKEN: ""
  VALID_TIME: ""
  TIME_PERIOD: ""
  QUERY_FREQ: ""
  LOGGING_LEVEL: ""

# argocd bug,so disable topologySpreadConstraints ref: https://github.com/argoproj/argo-cd/issues/16400
# topologySpreadConstraints:
#   - maxSkew: 1
#     topologyKey: topology.kubernetes.io/zone
#     whenUnsatisfiable: ScheduleAnyway
#     labelSelector:
#       matchLabels:
#         app.kubernetes.io/instance:  "{{ .Release.Name }}"
#   - maxSkew: 1
#     topologyKey: kubernetes.io/hostname
#     whenUnsatisfiable: DoNotSchedule
#     labelSelector:
#       matchLabels:
#         app.kubernetes.io/name: "{{ .Release.Name }}"
