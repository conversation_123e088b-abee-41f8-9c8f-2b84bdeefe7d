# Default values for jcl-coco-live-to-vod-status-checker-helm.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true

replicaCount: 1

image:
  repository: "138046196805.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-coco-live-to-vod-status-checker"
  # Overrides the image tag whose default is the chart appVersion.
  # The image.tag overrides argocd application imageTag in meta/values-<env>.yaml.
  # Overrides Top Down : argocd application imageTag <- chart appVersion <- containers image.tag
  tag: "1.19.1108"
  pullPolicy: IfNotPresent

nameOverride: "live-to-vod-status-checker"
fullnameOverride: ""

strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 1

service: {}

ingress: {}

resources:
  limits:
    cpu: 200m
    memory: 300Mi
  requests:
    cpu: 150m
    memory: 250Mi

# nodeSelector:
#   purpose: nodes

tolerations: []

# affinity:
#   nodeAffinity:
#     requiredDuringSchedulingIgnoredDuringExecution:
#       nodeSelectorTerms:
#       - matchExpressions:
#         - key: purpose
#           operator: In
#           values:
#           - nodes
#   podAntiAffinity:
#     preferredDuringSchedulingIgnoredDuringExecution:
#     - weight: 100
#       podAffinityTerm:
#         labelSelector:
#           matchExpressions:
#           - key: app.kubernetes.io/name
#             operator: In
#             values:
#             - live-to-vod-status-checker
#         topologyKey: kubernetes.io/hostname

horizontalPodAutoscaler:
  enabled: false

configMap:
  enabled: false
  defaultData: {}

configMapEnv:
  API_URL_INTERNAL: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/"
  API_URL_MANAGER: "http://{{ .Release.Namespace }}-linear-tv-manager.{{ .Release.Namespace }}"
  CHANNEL_NAME: "saku-log-preprod-catchup"
  VENDER_TOKEN: "saku"
  VALID_TIME: "900"
  TIME_PERIOD: "1800"
  QUERY_FREQ: "45"
  LOGGING_LEVEL: "INFO"
