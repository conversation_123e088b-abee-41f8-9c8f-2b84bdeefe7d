# Default values for coco-linear-automatic-switch.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true

replicaCount: 1

image:
  repository: "138046196805.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-coco-linear-automatic-switch"
  tag: ""
  pullPolicy: IfNotPresent

command:
  - "python3.7"
  - "/linear_switch/main.py"
  - "--media_live"
  - "Yes"
  - "--media_conn"
  - "Yes"

strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 1

nameOverride: "coco-linear-automatic-switch"
fullnameOverride: ""

resources:
  requests:
    cpu: 100m
    memory: 100Mi
  limits:
    cpu: 200m
    memory: 200Mi

nodeSelector:
  purpose: nodes

tolerations: []

affinity: {}


secret:
  aws:
    enabled: true

configMapEnv:
  AWS_DEFAULT_PROFILE: jcl
  API_URL_INTERNAL: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/"
  ENV: stage
  AWS_CONFIG_FILE: /etc/aws/config
  AWS_SHARED_CREDENTIALS_FILE: /etc/aws/credentials
  SLACK_CHANNEL: "jcl-log-stag-catchup"

configMapFile:
  mediaconnect_channel_list.json: |
    []

secretEnv: #???

# argocd bug,so disable topologySpreadConstraints ref: https://github.com/argoproj/argo-cd/issues/16400
# topologySpreadConstraints:
#   - maxSkew: 1
#     topologyKey: topology.kubernetes.io/zone
#     whenUnsatisfiable: ScheduleAnyway
#     labelSelector:
#       matchLabels:
#         app.kubernetes.io/instance:  "{{ .Release.Name }}"
#   - maxSkew: 1
#     topologyKey: kubernetes.io/hostname
#     whenUnsatisfiable: DoNotSchedule
#     labelSelector:
#       matchLabels:
#         app.kubernetes.io/name: "{{ .Release.Name }}"
