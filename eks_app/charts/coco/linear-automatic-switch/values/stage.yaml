# Default values for coco-linear-automatic-switch.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true

replicaCount: 0

image:
  repository: "138046196805.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-coco-linear-automatic-switch"
  tag: "94581a75"
  pullPolicy: IfNotPresent

command:
  - "python3.7"
  - "/linear_switch/main.py"
  - "--media_live"
  - "Yes"
  - "--media_conn"
  - "Yes"

strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 1

nameOverride: "coco-linear-automatic-switch"
fullnameOverride: ""

resources:
  requests:
    cpu: 100m
    memory: 100Mi
  limits:
    cpu: 200m
    memory: 200Mi

nodeSelector:
  purpose: nodes

tolerations: []

affinity: {}

secret:
  aws:
    enabled: true

configMapEnv:
  AWS_DEFAULT_PROFILE: saku
  API_URL_INTERNAL: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/"
  ENV: stag
  AWS_CONFIG_FILE: /etc/aws/config
  AWS_SHARED_CREDENTIALS_FILE: /etc/aws/credentials
  SLACK_CHANNEL: "howardzhou-webhook-stag"

configMapFile:
  mediaconnect_channel_list.json: |
    {
    "saku":
      [
        {
          "channel_type":"IP",
          "channel_id":"LIVECH01SVOD.com",
          "media_conn_primary":"saku-stag-01-a",
          "media_conn_backup":"saku-stag-01-d",
          "media_live":"saku-stag-01",
          "media_package":"saku-stag-01",
          "media_package_a":"",
          "media_package_b":""
        },
        {
          "channel_type":"RF",
          "channel_id":"17140",
          "media_conn_primary":"saku-stag-02-a",
          "media_conn_backup":"saku-stag-02-d",
          "media_live":"saku-stag-02",
          "media_package":"saku-stag-02",
          "media_package_a":"",
          "media_package_b":""
        },
        {
          "channel_type":"RF",
          "channel_id":"16941",
          "media_conn_primary":"saku-stag-03-a",
          "media_conn_backup":"saku-stag-03-d",
          "media_live":"saku-stag-03",
          "media_package":"saku-stag-03",
          "media_package_a":"",
          "media_package_b":""
        }
      ]
    }
