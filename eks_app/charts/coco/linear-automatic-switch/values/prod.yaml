# Default values for coco-linear-automatic-switch.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true

replicaCount: 1

image:
  repository: "138046196805.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-coco-linear-automatic-switch"
  tag: ""
  pullPolicy: IfNotPresent

command:
  - "python3.7"
  - "/linear_switch/main.py"
  - "--media_live"
  - "Yes"
  - "--media_conn"
  - "Yes"

strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 1

nameOverride: "coco-linear-automatic-switch"
fullnameOverride: ""

resources:
  requests:
    cpu: 100m
    memory: 100Mi
  limits:
    cpu: 200m
    memory: 200Mi

nodeSelector:
  purpose: nodes

tolerations: []

affinity: {}

secret:
  aws:
    enabled: true

configMapEnv:
  AWS_DEFAULT_PROFILE: saku
  API_URL_INTERNAL: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/"
  ENV: prod
  AWS_CONFIG_FILE: /etc/aws/config
  AWS_SHARED_CREDENTIALS_FILE: /etc/aws/credentials
  SLACK_CHANNEL: "saku-linear-switch"

configMapFile:
  mediaconnect_channel_list.json: |
    {
          "saku":
            [
              {
                "channel_type": "IP",
                "channel_id": "LIVECH01SVOD.com",
                "media_conn_primary": "saku-production-01-a",
                "media_conn_backup": "saku-production-01-d",
                "media_live": "saku-prod-01"
              },
              {
                "channel_type": "IP",
                "channel_id": "LIVECH08SVOD.com",
                "media_conn_primary": "saku-production-02-a",
                "media_conn_backup": "saku-production-02-d",
                "media_live": "saku-prod-02"
              },
              {
                "channel_type": "IP",
                "channel_id": "LIVECH09SVOD.com",
                "media_conn_primary": "saku-production-03-a",
                "media_conn_backup": "saku-production-03-d",
                "media_live": "saku-prod-03"
              },
              {
                "channel_type": "IP",
                "channel_id": "LIVECH10SVOD.com",
                "media_conn_primary": "saku-production-04-a",
                "media_conn_backup": "saku-production-04-d",
                "media_live": "saku-prod-04"
              },
              {
                "channel_type": "IP",
                "channel_id": "LIVEFOXSVOD.com",
                "media_conn_primary": "saku-production-05-a",
                "media_conn_backup": "saku-production-05-d",
                "media_live": "saku-prod-05"
              },
              {
                "channel_type": "IP",
                "channel_id": "LIVECH12SVOD.com",
                "media_conn_primary": "saku-production-06-a",
                "media_conn_backup": "saku-production-06-d",
                "media_live": "saku-prod-06"
              },
              {
                "channel_type": "IP",
                "channel_id": "LIVEJSPOSTDSVOD.com1",
                "media_conn_primary": "saku-production-07-a",
                "media_conn_backup": "saku-production-07-d",
                "media_live": "saku-prod-07"
              },
              {
                "channel_type": "IP",
                "channel_id": "LIVEJSPOSTDSVOD.com2",
                "media_conn_primary": "saku-production-08-a",
                "media_conn_backup": "saku-production-08-d",
                "media_live": "saku-prod-08"
              },
              {
                "channel_type": "IP",
                "channel_id": "LIVEJSPOSTDSVOD.com3",
                "media_conn_primary": "saku-production-09-a",
                "media_conn_backup": "saku-production-09-d",
                "media_live": "saku-prod-09"
              },
              {
                "channel_type": "IP",
                "channel_id": "LIVEJSPOSTDSVOD.com4",
                "media_conn_primary": "saku-production-10-a",
                "media_conn_backup": "saku-production-10-d",
                "media_live": "saku-prod-10"
              },
              {
                "channel_type": "IP",
                "channel_id": "LIVEGOLFNETSVOD.com",
                "media_conn_primary": "saku-production-11-a",
                "media_conn_backup": "saku-production-11-d",
                "media_live": "saku-prod-11"
              },
              {
                "channel_type": "IP",
                "channel_id": "LIVECH02SVOD.com",
                "media_conn_primary": "saku-production-12-a",
                "media_conn_backup": "saku-production-12-d",
                "media_live": "saku-prod-12"
              },
              {
                "channel_type": "IP",
                "channel_id": "LIVECH03SVOD.com",
                "media_conn_primary": "saku-production-13-a",
                "media_conn_backup": "saku-production-13-d",
                "media_live": "saku-prod-13"
              },
              {
                "channel_type": "IP",
                "channel_id": "LIVECH11SVOD.com",
                "media_conn_primary": "saku-production-14-a",
                "media_conn_backup": "saku-production-14-d",
                "media_live": "saku-prod-14"
              },
              {
                "channel_type": "IP",
                "channel_id": "LIVECNBCSVOD.com",
                "media_conn_primary": "saku-production-15-a",
                "media_conn_backup": "saku-production-15-d",
                "media_live": "saku-prod-15"
              },
              {
                "channel_type": "IP",
                "channel_id": "LIVEGTASUSVOD.com",
                "media_conn_primary": "saku-production-48-a",
                "media_conn_backup": "saku-production-48-d",
                "media_live": "saku-prod-48"
              },
              {
                "channel_type": "IP",
                "channel_id": "LIVECH13SVOD.com",
                "media_conn_primary": "saku-production-49-a",
                "media_conn_backup": "saku-production-49-d",
                "media_live": "saku-prod-49"
              },
              {
                "channel_type": "RF",
                "channel_id": "17140",
                "media_conn_primary": "saku-production-26-a",
                "media_conn_backup": "saku-production-26-d",
                "media_live": "saku-prod-26"
              },
              {
                "channel_type": "RF",
                "channel_id": "16941",
                "media_conn_primary": "saku-production-27-a",
                "media_conn_backup": "saku-production-27-d",
                "media_live": "saku-prod-27"
              },
              {
                "channel_type": "RF",
                "channel_id": "16735",
                "media_conn_primary": "saku-production-28-a",
                "media_conn_backup": "saku-production-28-d",
                "media_live": "saku-prod-28"
              },
              {
                "channel_type": "RF",
                "channel_id": "16736",
                "media_conn_primary": "saku-production-29-a",
                "media_conn_backup": "saku-production-29-d",
                "media_live": "saku-prod-29"
              },
              {
                "channel_type": "RF",
                "channel_id": "16938",
                "media_conn_primary": "saku-production-30-a",
                "media_conn_backup": "saku-production-30-d",
                "media_live": "saku-prod-30"
              },
              {
                "channel_type": "RF",
                "channel_id": "17147",
                "media_conn_primary": "saku-production-31-a",
                "media_conn_backup": "saku-production-31-d",
                "media_live": "saku-prod-31"
              },
              {
                "channel_type": "RF",
                "channel_id": "17037",
                "media_conn_primary": "saku-production-32-a",
                "media_conn_backup": "saku-production-32-d",
                "media_live": "saku-prod-32"
              },
              {
                "channel_type": "RF",
                "channel_id": "17036",
                "media_conn_primary": "saku-production-33-a",
                "media_conn_backup": "saku-production-33-d",
                "media_live": "saku-prod-33"
              },
              {
                "channel_type": "RF",
                "channel_id": "16985",
                "media_conn_primary": "saku-production-34-a",
                "media_conn_backup": "saku-production-34-d",
                "media_live": "saku-prod-34"
              },
              {
                "channel_type": "RF",
                "channel_id": "17304",
                "media_conn_primary": "saku-production-35-a",
                "media_conn_backup": "saku-production-35-d",
                "media_live": "saku-prod-35"
              },
              {
                "channel_type": "RF",
                "channel_id": "16787",
                "media_conn_primary": "saku-production-36-a",
                "media_conn_backup": "saku-production-36-d",
                "media_live": "saku-prod-36"
              },
              {
                "channel_type": "RF",
                "channel_id": "16835",
                "media_conn_primary": "saku-production-37-a",
                "media_conn_backup": "saku-production-37-d",
                "media_live": "saku-prod-37"
              },
              {
                "channel_type": "RF",
                "channel_id": "16884",
                "media_conn_primary": "saku-production-38-a",
                "media_conn_backup": "saku-production-38-d",
                "media_live": "saku-prod-38"
              },
              {
                "channel_type": "RF",
                "channel_id": "17086",
                "media_conn_primary": "saku-production-40-a",
                "media_conn_backup": "saku-production-40-d",
                "media_live": "saku-prod-40"
              },
              {
                "channel_type": "RF",
                "channel_id": "16939",
                "media_conn_primary": "saku-production-41-a",
                "media_conn_backup": "saku-production-41-d",
                "media_live": "saku-prod-41"
              },
              {
                "channel_type": "RF",
                "channel_id": "17141",
                "media_conn_primary": "saku-production-42-a",
                "media_conn_backup": "saku-production-42-d",
                "media_live": "saku-prod-42"
              },
              {
                "channel_type": "RF",
                "channel_id": "16889",
                "media_conn_primary": "saku-production-43-a",
                "media_conn_backup": "saku-production-43-d",
                "media_live": "saku-prod-43"
              },
              {
                "channel_type": "RF",
                "channel_id": "17084",
                "media_conn_primary": "saku-production-44-a",
                "media_conn_backup": "saku-production-44-d",
                "media_live": "saku-prod-44"
              },
              {
                "channel_type": "RF",
                "channel_id": "17137",
                "media_conn_primary": "saku-production-45-a",
                "media_conn_backup": "saku-production-45-d",
                "media_live": "saku-prod-45"
              },
              {
                "channel_type": "RF",
                "channel_id": "16789",
                "media_conn_primary": "saku-production-46-a",
                "media_conn_backup": "saku-production-46-d",
                "media_live": "saku-prod-46"
              },
              {
                "channel_type": "RF",
                "channel_id": "16734",
                "media_conn_primary": "saku-production-47-a",
                "media_conn_backup": "saku-production-47-d",
                "media_live": "saku-prod-47"
              }
            ]
      }
