{{- if .Values.enabled -}}
{{- $fullname := include "coco-linear-automatic-switch.fullname" . }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $fullname }}
  labels:
    app.kubernetes.io/name: {{ include "coco-linear-automatic-switch.name" . }}
    helm.sh/chart: {{ include "coco-linear-automatic-switch.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  replicas: {{ .Values.replicaCount }}
  strategy:
    {{- toYaml .Values.strategy | nindent 4 }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "coco-linear-automatic-switch.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
      labels:
        app.kubernetes.io/name: {{ include "coco-linear-automatic-switch.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          {{- with .Values.command }}
          command: 
            {{- toYaml . | nindent 10 }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          envFrom:
            - configMapRef:
                name: {{ $fullname }}-env-config
          volumeMounts:
            {{- if .Values.secret.aws.enabled }}
            - name: aws-config
              mountPath: "/etc/aws"
            {{- end }}
            {{- if .Values.configMapFile }}
            - name: config-file
              mountPath: /switch_config
            {{- end }}
      volumes:
        {{- if .Values.secret.aws.enabled }}
        - name: aws-config
          secret:
            secretName: {{ $fullname }}-aws-config
        {{- end }}
        {{- if .Values.configMapFile }}
        - name: config-file
          configMap:
            name: {{ $fullname }}-config-file
      {{- end }}
      {{/*
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if not .Values.affinity }}
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: purpose
                operator: In
                values:
                - {{ .Values.nodeSelector.purpose }}
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app.kubernetes.io/name
                  operator: In
                  values:
                  - {{ include "coco-linear-automatic-switch.name" . }}
              topologyKey: kubernetes.io/hostname
      {{- else }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- end }}
      */}}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
{{- end }}