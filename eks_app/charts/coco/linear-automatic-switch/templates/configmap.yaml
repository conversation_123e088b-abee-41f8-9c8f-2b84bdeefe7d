{{- if .Values.enabled -}}
{{- $fullname := include "coco-linear-automatic-switch.fullname" . }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullname }}-env-config
data:
  {{- range $key, $val := .Values.configMapEnv }}
    {{- if $val }}
      {{ $key }}: {{tpl $val $ | quote }}
    {{- else }}
      {{ $key }}: ""
    {{- end }}
  {{- end }}

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullname }}-config-file
data:
  {{- range $key, $val := .Values.configMapFile }}
    {{- if $val }}
      {{ $key }}: {{ $val | quote }}
    {{- else }}
      {{ $key }}: ""
    {{- end }}
  {{- end }}
{{- end }}