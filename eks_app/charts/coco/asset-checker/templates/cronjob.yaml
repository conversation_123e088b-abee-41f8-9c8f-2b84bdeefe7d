{{- if .Values.enabled -}}
{{ $fullname := include "coco-asset-checker.fullname" . }}
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ $fullname }}
  labels:
    app.kubernetes.io/name: {{ include "coco-asset-checker.name" . }}
    helm.sh/chart: {{ include "coco-asset-checker.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  schedule: {{ .Values.schedule | quote }}
  concurrencyPolicy: {{ .Values.concurrencyPolicy }}
  failedJobsHistoryLimit: {{ .Values.failedJobsHistoryLimit }}
  successfulJobsHistoryLimit: {{ .Values.successfulJobsHistoryLimit }}
  jobTemplate:
    spec:
      template:
        metadata:
          annotations:
            checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
          labels:
            app.kubernetes.io/name: {{ include "coco-asset-checker.name" . }}
            app.kubernetes.io/instance: {{ .Release.Name }}
        spec:
          restartPolicy: "{{ .Values.restartPolicy }}"
          containers:
            - name: "live-to-vod-trigger"
              image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default $.Chart.AppVersion }}"
              imagePullPolicy: {{ .Values.image.pullPolicy }}
              command: ["/usr/local/bin/python", "/app/main.py", "--liveToVodTrigger"]
              resources:
                {{- toYaml .Values.resources | nindent 16 }}
              envFrom:
                - configMapRef:
                    name: {{ $fullname }}-env-config
              {{- if .Values.secret.awsConfig.enabled }}
              volumeMounts:
                - name: aws-config
                  mountPath: "/etc/aws"
              {{- end }}
            - name: "asset-deletable-checker"
              image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default $.Chart.AppVersion }}"
              imagePullPolicy: {{ .Values.image.pullPolicy }}
              command: ["/usr/local/bin/python", "/app/main.py", "--assetDeletableChecker"]
              resources:
                {{- toYaml .Values.resources | nindent 16 }}
              envFrom:
                - configMapRef:
                    name: {{ $fullname }}-env-config
          {{- if .Values.secret.awsConfig.enabled }}
              volumeMounts:
                - name: aws-config
                  mountPath: "/etc/aws"  
          volumes:
            - name: aws-config
              secret:
                secretName: {{ $fullname }}-aws-config
          {{- end }}
          {{- with .Values.nodeSelector }}
          nodeSelector:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{/*
          {{- if not .Values.affinity }}
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: purpose
                    operator: In
                    values:
                    - {{ .Values.nodeSelector.purpose }}
            podAntiAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - weight: 100
                podAffinityTerm:
                  labelSelector:
                    matchExpressions:
                    - key: app.kubernetes.io/name
                      operator: In
                      values:
                      - {{ include "coco-asset-checker.name" . }}
                  topologyKey: kubernetes.io/hostname
          {{- else }}
          {{- with .Values.affinity }}
          affinity:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- end }}
          */}}
          {{- with .Values.tolerations }}
          tolerations:
            {{- toYaml . | nindent 12 }}
          {{- end }}
{{- end }}
