# Default values for jcl-coco-asset-checker-helm.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true

replicaCount: 1

image:
  repository: "138046196805.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-coco-asset-checker"
  tag: "1.24.0606"
  pullPolicy: IfNotPresent

nameOverride: "asset-checker"
fullnameOverride: ""

strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 1

service: {}

schedule: "0 1 * * *"

concurrencyPolicy: "Forbid"

failedJobsHistoryLimit: 3

successfulJobsHistoryLimit: 1

restartPolicy: "OnFailure"

ingress: {}

resources:
  limits:
    cpu: 120m
    memory: 200Mi
  requests:
    cpu: 100m
    memory: 150Mi

# nodeSelector:
#   purpose: nodes

tolerations: []

affinity: {}

horizontalPodAutoscaler:
  enabled: false

configMapEnv:
  AWS_DEFAULT_PROFILE: "saku"
  API_URL_INTERNAL: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/"
  API_URL_MANAGER: "http://{{ .Release.Namespace }}-linear-tv-manager.{{ .Release.Namespace }}"
  AWS_CONFIG_FILE: /etc/aws/config
  AWS_SHARED_CREDENTIALS_FILE: /etc/aws/credentials
  VENDER_TOKEN: "saku"
  LOGGING_LEVEL: "INFO"
  AWS_REGION_NAME: "ap-northeast-1"
  DYNAMODB_TABLE_NAME: "saku-prep-l2v-stage-HarvestSchedulesTable"
  SLACK_CHANNEL: "saku-log-preprod-catchup"

secret:
  awsConfig:
    enabled: true
