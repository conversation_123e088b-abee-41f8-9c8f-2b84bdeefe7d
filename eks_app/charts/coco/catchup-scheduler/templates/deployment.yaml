{{- if .Values.enabled }}
{{- $fullname := include "coco-catchup-scheduler.fullname" . -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $fullname }}
  labels:
    {{- include "coco-catchup-scheduler.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "coco-catchup-scheduler.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
      labels:
        {{- include "coco-catchup-scheduler.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- $volumes := len .Values.volumes -}}
      {{- if gt $volumes 0 }}
      volumes:
        {{- tpl (toYaml .Values.volumes) . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "coco-catchup-scheduler.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: catchup-handler
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          {{- with .Values.catchupHandler }}
          image: "{{ .image.repository }}:{{ .image.tag | default $.Chart.AppVersion }}"
          imagePullPolicy: {{ .image.pullPolicy }}
          command:
            {{- toYaml .command | nindent 12 }}
          args:
            {{- toYaml .args | nindent 12 }}
          resources:
            {{- toYaml .resources | nindent 12 }}
          envFrom:
            - configMapRef:
                name: {{ $fullname }}-env
            - secretRef:
                name: {{ $fullname }}-env
          {{- $volumeMount := len .volumeMounts -}}
          {{- if gt $volumeMount 0 }}
          volumeMounts:
            {{- tpl (toYaml .volumeMounts) $ | nindent 12 }}
          {{- end }}
          {{- end }}
        - name: status-handler
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          {{- with .Values.statusHandler }}
          image: "{{ .image.repository }}:{{ .image.tag | default $.Chart.AppVersion }}"
          imagePullPolicy: {{ .image.pullPolicy }}
          command:
            {{- toYaml .command | nindent 12 }}
          args:
            {{- toYaml .args | nindent 12 }}
          resources:
            {{- toYaml .resources | nindent 12 }}
          envFrom:
            - configMapRef:
                name: {{ $fullname }}-env
            - secretRef:
                name: {{ $fullname }}-env
          {{- $volumeMount := len .volumeMounts -}}
          {{- if gt $volumeMount 0 }}
          volumeMounts:
            {{- tpl (toYaml .volumeMounts) $  | nindent 12 }}
          {{- end }}
          {{- end }}

      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- tpl (toYaml .) $ | nindent 8 }}
      {{- end }}
{{- end }}
