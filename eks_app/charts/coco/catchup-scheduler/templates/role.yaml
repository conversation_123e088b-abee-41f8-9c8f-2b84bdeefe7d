{{- if and .Values.enabled .Values.serviceAccount.create -}}
{{- $serviceAccountName := include "coco-catchup-scheduler.serviceAccountName" . -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ $serviceAccountName }}
  labels:
    {{- include "coco-catchup-scheduler.labels" . | nindent 4 }}
rules:
  - apiGroups:
      - ""
    resources:
      - pods
    verbs:
      - list
      - get
      - create
  - apiGroups:
      - ""
    resources:
      - namespaces
    verbs:
      - list
      - create
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ $serviceAccountName }}
  labels:
    {{- include "coco-catchup-scheduler.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ $serviceAccountName }}
subjects:
  - kind: ServiceAccount
    name: {{ $serviceAccountName }}
{{- end }}
