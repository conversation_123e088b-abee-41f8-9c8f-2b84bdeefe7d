{{- if .Values.enabled }}
{{- $fullname := include "coco-catchup-scheduler.fullname" . -}}
{{- $live2vodWorkerNamespace := .Release.Namespace }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullname }}-env
  labels:
    {{- include "coco-catchup-scheduler.labels" . | nindent 4 }}
data:
  {{- range $key, $val := .Values.configMapEnv }}
    {{- if $val }}
      {{ $key }}: {{ tpl $val $ | quote }}
    {{- else }}
      {{ $key }}: ""
    {{- end }}
  {{- end }}
{{- end }}
