# Default values for sample.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true
enableTest: false
includeAwsCredential: true
replicaCount: 1

catchupHandler:
  image:
    repository: 138046196805.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-coco-catchup-scheduler
    pullPolicy: IfNotPresent
    # Overrides the image tag whose default is the chart appVersion.
    tag: ""
  resources:
    # We usually recommend not to specify default resources and to leave this as a conscious
    # choice for the user. This also increases chances charts run on environments with little
    # resources, such as Minikube. If you do want to specify resources, uncomment the following
    # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
    limits:
      cpu: 500m
      memory: 500Mi
    requests:
      cpu: 200m
      memory: 250Mi
  command:
    - /bin/sh
    - -c
    - /usr/local/bin/python /app/main.py catchup_handler
  volumeMounts:
    - mountPath: /live_to_vod_config
      name: live2vod-worker-tpl
    - mountPath: /thumbnail_config
      name: thumbnail-worker-tpl
    # - mountPath: /etc/aws
    #   name: aws-config

statusHandler:
  image:
    repository: 138046196805.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-coco-catchup-scheduler
    pullPolicy: IfNotPresent
    # Overrides the image tag whose default is the chart appVersion.
    tag: ""
  resources:
    # We usually recommend not to specify default resources and to leave this as a conscious
    # choice for the user. This also increases chances charts run on environments with little
    # resources, such as Minikube. If you do want to specify resources, uncomment the following
    # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
    limits:
      cpu: 500m
      memory: 500Mi
    requests:
      cpu: 200m
      memory: 250Mi
  command:
    - /bin/sh
    - -c
    - /usr/local/bin/python /app/main.py status_handler
  volumeMounts:
    - mountPath: /live_to_vod_config
      name: live2vod-worker-tpl
    - mountPath: /thumbnail_config
      name: thumbnail-worker-tpl
    # - mountPath: /etc/aws
    #   name: aws-config

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 50
  targetMemoryUtilizationPercentage: 50

vpa:
  enabled: true
  resources:
    maxAllowed:
      cpu: 400m
      memory: 400Mi
    minAllowed:
      cpu: 100m
      memory: 100Mi

volumes:
  - configMap:
      name: coco-live2vod-worker-tpl
    name: live2vod-worker-tpl
  - configMap:
      name: coco-thumbnail-worker-tpl
    name: thumbnail-worker-tpl
  # - secret:
  #     secretName: coco-catchup-scheduler-aws-credential
  #   name: aws-config

nodeSelector: {}

tolerations: []

affinity: {}

configMapEnv:
  API_URL_HARVEST:
  API_URL_INTERNAL:
  API_URL_MANAGER:
  API_URL_TRIM:
  # AWS_CONFIG_FILE:
  # AWS_DEFAULT_PROFILE:
  # AWS_SHARED_CREDENTIALS_FILE:
  ENV:
  LIVE2VOD_ASSET_BACUKETNAME:
  LIVE2VOD_PACKAGING_GROUP:
  # LIVE2VOD_ROLE_ARN:
  S3_LIVE2VOD_THEATER:
  S3_MANAGER_DATA:
  SQS_URL_CATCHUP:
  SQS_URL_HARVEST_STATUS:
  SQS_URL_MEDIAPACKAGE_VOD_STATUS:
  SQS_URL_TRIM_STATUS:

secretEnv:
  API_KEY_HARVEST:
  API_KEY_TRIM:
  SENTRY_DSN:
  URL_SLACK_WEBHOOK:
  URL_SLACK_WEBHOOK_O2:

secret: #for aws-config
  aws:
    enabled: false

# argocd bug,so disable topologySpreadConstraints ref: https://github.com/argoproj/argo-cd/issues/16400
# topologySpreadConstraints:
#   - maxSkew: 1
#     topologyKey: topology.kubernetes.io/zone
#     whenUnsatisfiable: ScheduleAnyway
#     labelSelector:
#       matchLabels:
#         app.kubernetes.io/instance:  "{{ .Release.Name }}"
#   - maxSkew: 1
#     topologyKey: kubernetes.io/hostname
#     whenUnsatisfiable: DoNotSchedule
#     labelSelector:
#       matchLabels:
#         app.kubernetes.io/name: "{{ .Release.Name }}"

pdb:
  enabled: true
  maxUnavailable: 0
