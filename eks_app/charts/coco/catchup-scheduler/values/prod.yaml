# Default values for sample.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true
enableTest: false
includeAwsCredential: true
replicaCount: 1

catchupHandler:
  image:
    repository: 138046196805.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-coco-catchup-scheduler
    pullPolicy: IfNotPresent
    # Overrides the image tag whose default is the chart appVersion.
    # The image.tag overrides argocd application imageTag in meta/values-<env>.yaml.
    # Overrides Top Down : argocd application imageTag <- chart appVersion <- containers image.tag
    tag: ""
  resources:
    # We usually recommend not to specify default resources and to leave this as a conscious
    # choice for the user. This also increases chances charts run on environments with little
    # resources, such as Minikube. If you do want to specify resources, uncomment the following
    # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
    limits:
      cpu: 500m
      memory: 500Mi
    requests:
      cpu: 200m
      memory: 250Mi
  command:
    - /bin/sh
    - -c
    - /usr/local/bin/python /app/main.py catchup_handler
  volumeMounts:
    - mountPath: /live_to_vod_config
      name: "{{ .Release.Namespace }}-live2vod-tpl"
    - mountPath: /thumbnail_config
      name: "{{ .Release.Namespace }}-thumbnail-tpl"
    - mountPath: /etc/aws
      name: aws-config

statusHandler:
  image:
    repository: 138046196805.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-coco-catchup-scheduler
    pullPolicy: IfNotPresent
    # Overrides the image tag whose default is the chart appVersion.
    tag: ""
  resources:
    # We usually recommend not to specify default resources and to leave this as a conscious
    # choice for the user. This also increases chances charts run on environments with little
    # resources, such as Minikube. If you do want to specify resources, uncomment the following
    # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
    limits:
      cpu: 500m
      memory: 500Mi
    requests:
      cpu: 500m
      memory: 500Mi
  command:
    - /bin/sh
    - -c
    - /usr/local/bin/python /app/main.py status_handler
  volumeMounts:
    - mountPath: /live_to_vod_config
      name: "{{ .Release.Namespace }}-live2vod-tpl"
    - mountPath: /thumbnail_config
      name: "{{ .Release.Namespace }}-thumbnail-tpl"
    - mountPath: /etc/aws
      name: aws-config

volumes:
  - configMap:
      name: "{{ .Release.Namespace }}-live2vod-tpl"
    name: "{{ .Release.Namespace }}-live2vod-tpl"
  - configMap:
      name: "{{ .Release.Namespace }}-thumbnail-tpl"
    name: "{{ .Release.Namespace }}-thumbnail-tpl"
  - secret:
      secretName: "{{ .Release.Name }}-aws-credential"
    name: aws-config

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 25
  targetCPUUtilizationPercentage: 50
  targetMemoryUtilizationPercentage: 50

vpa:
  enabled: false
  resources:
    maxAllowed:
      cpu: 250m
      memory: 300Mi
    minAllowed:
      cpu: 200m
      memory: 250Mi

nodeSelector: {}

tolerations: []

affinity: {}

configMapEnv:
  API_URL_INTERNAL: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/"
  API_URL_MANAGER: "http://{{ .Release.Namespace }}-linear-tv-manager.{{ .Release.Namespace }}"
  ENV: prod
  API_URL_HARVEST: "https://dzbvmnh0i4.execute-api.ap-northeast-1.amazonaws.com/prod/harvest"
  API_URL_TRIM: "https://dzbvmnh0i4.execute-api.ap-northeast-1.amazonaws.com/prod/trim"
  S3_LIVE2VOD_THEATER: "s3://kks-prod-saku-live2vod-theater/"
  S3_MANAGER_DATA: "s3://jcl-coco-data/prod/"
  SQS_URL_CATCHUP: "https://sqs.ap-northeast-1.amazonaws.com/138046196805/saku-prod-catchup"
  SQS_URL_HARVEST_STATUS: "https://sqs.ap-northeast-1.amazonaws.com/138046196805/saku-prod-harvest-status"
  SQS_URL_TRIM_STATUS: "https://sqs.ap-northeast-1.amazonaws.com/138046196805/saku-prod-trim-status"
  AWS_CONFIG_FILE: /etc/aws/config
  AWS_DEFAULT_PROFILE: saku
  AWS_SHARED_CREDENTIALS_FILE: /etc/aws/credentials

secretEnv:
  API_KEY_HARVEST: "HjiQzDDrJg7MCx3v01U634ZGGWQddEWT4mSYuc6D"
  API_KEY_TRIM: "HjiQzDDrJg7MCx3v01U634ZGGWQddEWT4mSYuc6D"
  URL_SLACK_WEBHOOK: "*****************************************************************************"
  URL_SLACK_WEBHOOK_O2: "*******************************************************************************"

secret: #for aws-config
  aws:
    enabled: true
