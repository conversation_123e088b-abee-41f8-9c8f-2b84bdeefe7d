{{- if .Values.enableTest }}
apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "coco-linear-tv-manager.fullname" . }}-test-connection"
  labels:
    {{- include "coco-linear-tv-manager.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "coco-linear-tv-manager.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
{{- end }}
