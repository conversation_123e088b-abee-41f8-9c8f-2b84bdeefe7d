{{- if .Values.enabled }}
{{- $fullname := include "coco-linear-tv-manager.fullname" . -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullname }}-env
  labels:
    {{- include "coco-linear-tv-manager.labels" . | nindent 4 }}
data:
  {{- range $key, $val := .Values.configMapEnv }}
    {{- if $val }}
      {{ $key }}: {{ tpl $val $ | quote }}
    {{- else }}
      {{ $key }}: ""
    {{- end }}
  {{- end }}
{{- end }}
