# Default values for sample.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

enabled: true
enableTest: false
includeAwsCredential: true
replicaCount: 1

image:
  repository: 138046196805.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-coco-manager
  pullPolicy: IfNotPresent  
  # Overrides the image tag whose default is the chart appVersion.
  # The image.tag overrides argocd application imageTag in meta/values-<env>.yaml. 
  # Overrides Top Down : argocd application imageTag <- chart appVersion <- containers image.tag
  tag: ""

volumes:
  - secret:
      secretName: "{{ .Release.Name }}-aws-credential"
    name: aws-config

volumeMounts:
  - mountPath: /etc/aws
    name: aws-config

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  className: "alb"
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: "/api/healthz"
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
    alb.ingress.kubernetes.io/scheme: "internet-facing"
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/ssl-policy: "ELBSecurityPolicy-TLS13-1-2-2021-06"
    alb.ingress.kubernetes.io/subnets: "saku-prod-public-1c,saku-prod-public-1a"
    alb.ingress.kubernetes.io/group.name: 'saku-prod-coco'
    alb.ingress.kubernetes.io/group.order: '5'
    alb.ingress.kubernetes.io/security-groups: 'saku-prod-coco-elb-sg'
    alb.ingress.kubernetes.io/manage-backend-security-group-rules: 'true'
    external-dns.alpha.kubernetes.io/aws-weight: "100"
    external-dns.alpha.kubernetes.io/set-identifier: "saku-prod-eks"
  hosts:
    - host: coco-saku-manager.kkstream.tech
      paths:
        - path: /
          pathType: Prefix
  tls: []

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 400m
    memory: 500Mi
  requests:
    cpu: 200m
    memory: 200Mi

container:
  port: 80
  liveness:
    path: /healthz
    port: 80
  readiness:
    path: /api/healthz
    port: 80
  livenessInitialDelaySeconds: 60
  livenessPeriodSeconds: 60
  readinessInitialDelaySeconds: 10
  readinessPeriodSeconds: 30

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 25
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 70

vpa:
  enabled: false
  resources:
    maxAllowed:
      cpu: 400m
      memory: 500Mi
    minAllowed:
      cpu: 300m
      memory: 400Mi

nodeSelector: {}

tolerations: []

affinity: {}

configMapEnv:
  API_URL_INTERNAL: "http://{{ .Release.Namespace }}-internal-api.{{ .Release.Namespace }}/api/v1/"
  AWS_DEFAULT_PROFILE: saku
  AWS_CONFIG_FILE: /etc/aws/config
  AWS_SHARED_CREDENTIALS_FILE: /etc/aws/credentials
  CHANNEL_ID_CONVERT: '{
        "saku": {
          "LIVEJSPOSTDSVOD.com": {
            "1": "LIVEJSPOSTDSVOD.com1",
            "2": "LIVEJSPOSTDSVOD.com2",
            "3": "LIVEJSPOSTDSVOD.com3",
            "4": "LIVEJSPOSTDSVOD.com4"
          },
          "LIVEJSPOOPT1SVOD.com": {
            "1": "LIVEJSPOSTDSVOD.com1",
            "2": "LIVEJSPOSTDSVOD.com2",
            "3": "LIVEJSPOSTDSVOD.com3",
            "4": "LIVEJSPOSTDSVOD.com4"
          }
        }
      }'
  CHANNEL_ID_LIST: '{
        "saku": [
             "LIVECH01SVOD.com",
             "LIVECH08SVOD.com",
             "LIVECH09SVOD.com",
             "LIVECH10SVOD.com",
             "LIVEFOXSVOD.com",
             "LIVECH12SVOD.com",
             "LIVEJSPOSTDSVOD.com1",
             "LIVEJSPOSTDSVOD.com2",
             "LIVEJSPOSTDSVOD.com3",
             "LIVEJSPOSTDSVOD.com4",
             "LIVEGOLFNETSVOD.com",
             "LIVECH02SVOD.com",
             "LIVECH03SVOD.com",
             "LIVECH11SVOD.com",
             "LIVECNBCSVOD.com",
             "LIVEGTASUSVOD.com",
             "LIVECH13SVOD.com",
             "LIVECH14SVOD.com",
             "17140",
             "16941",
             "16735",
             "16736",
             "16938",
             "17147",
             "17037",
             "17036",
             "16985",
             "17304",
             "16787",
             "16835",
             "16884",
             "17086",
             "16939",
             "17141",
             "16889",
             "17084",
             "17137",
             "16789",
             "16734"]
      }'
  ENV: prod
  S3_MANAGER_DATA: "s3://jcl-coco-data/prod/"
  SQS_URL_CATCHUP: "https://sqs.ap-northeast-1.amazonaws.com/138046196805/saku-prod-catchup"
  DEBUG_METRICS: "true"

secretEnv:
  URL_SLACK_WEBHOOK: "*****************************************************************************"
  URL_SLACK_WEBHOOK_O2: "*******************************************************************************"
