locals {
  aws_region = "ap-northeast-1"
  env        = terraform.workspace == "stag" ? "stage" : terraform.workspace
  internet_gateway_id = {
    prod = "igw-0dd07520e6f335187"
    stag = "igw-015a9ea0445c4a485"
  }

  vpc_cidr = {
    prod = "***********/21"
    stag = "**********/21"
  }

  subnets = {
    prod = {
      "ap-northeast-1a" = {

        public = {
          eks = {
            cidr = "10.103.32.0/24"
            name = "saku-${terraform.workspace}-public-1a"
          }
          eks_utility = {
            cidr = "*************/27"
            name = "utility-ap-northeast-1a.saku-${terraform.workspace}-k8s.kkstream.tech"
          }
          nat = {
            cidr             = "***********/28"
            name             = "saku-${terraform.workspace}-nat-0"
            route_table_id   = "rtb-03a3fb00b8fa0b92c"
            nat_gateway_name = "saku-${terraform.workspace}-0"
            nat_eip          = "***********"
            nat_eip_name     = "saku-${terraform.workspace}-nat-0"
          }
          playready_elb = {
            cidr = "*************/28"
          }
        }
        private = {
          eks = {
            cidr           = "**********/20"
            name           = "saku-${terraform.workspace}-private-1a"
            route_table_id = "rtb-08852806bf9c21201"
            nat_gateway_id = "nat-01688f5d2c656056b"
          }
          db = {
            cidr = "************/28"
            name = "saku-${terraform.workspace}-db-ap-northeast-1a"
          }
          playready = {
            cidr           = "*************/28"
            nat_gateway_id = "nat-01688f5d2c656056b"
          }
          coco_lambda = {
            cidr           = "***********/23"
            name           = "ap-northeast-1a.saku-${terraform.workspace}-k8s.kkstream.tech"
            route_table_id = "rtb-03aebfb4e6e66c4b1"
            nat_gateway_id = "nat-01688f5d2c656056b"
          }
          lambda = {
            cidr = "10.101.17.0/28"
            name = "saku-${terraform.workspace}-lambda"
          }
          prometheus = {
            cidr           = "10.101.16.80/28"
            name           = "saku-${terraform.workspace}-prometheus_monitor_subnet"
            route_table_id = "rtb-01a151e47ce3d563b"
            nat_gateway_id = "nat-0bf0fab6c4ad2651b"
          }
        }
      }
      "ap-northeast-1c" = {
        public = {
          eks = {
            cidr = "10.103.33.0/24"
            name = "saku-${terraform.workspace}-public-1c"
          }
          eks_utility = {
            cidr = "*************/27"
            name = "utility-ap-northeast-1c.saku-${terraform.workspace}-k8s.kkstream.tech"
          }
          nat = {
            cidr             = "************/28"
            name             = "saku-${terraform.workspace}-nat-1"
            route_table_id   = "rtb-02f6f23076079ec52"
            nat_gateway_name = "saku-${terraform.workspace}-1"
            nat_eip          = "*************"
            nat_eip_name     = "saku-${terraform.workspace}-nat-1"
          }
          playready_elb = {
            cidr = "*************/28"
          }
        }
        private = {
          eks = {
            cidr           = "***********/20"
            name           = "saku-${terraform.workspace}-private-1c"
            route_table_id = "rtb-070c707bf2516abf0"
            nat_gateway_id = "nat-0bf0fab6c4ad2651b"
          }
          db = {
            cidr = "************/28"
            name = "saku-${terraform.workspace}-db-ap-northeast-1c"
          }
          playready = {
            cidr           = "*************/28"
            nat_gateway_id = "nat-0bf0fab6c4ad2651b"
          }
          coco_lambda = {
            cidr           = "***********/23"
            name           = "ap-northeast-1c.saku-${terraform.workspace}-k8s.kkstream.tech"
            route_table_id = "rtb-09970ceba25df47e7"
            nat_gateway_id = "nat-0bf0fab6c4ad2651b"
          }
          coco_lambda_1d = {
            cidr           = "10.101.18.0/23"
            name           = "ap-northeast-1d.saku-${terraform.workspace}-k8s.kkstream.tech"
            route_table_id = "rtb-09970ceba25df47e7"
            nat_gateway_id = "nat-0bf0fab6c4ad2651b"
          }
        }
      }
      "ap-northeast-1d" = {
        public = {
          jump = {
            cidr = "10.101.16.64/28"
          }
        }
      }
    }
    stag = {
      "ap-northeast-1a" = {

        public = {
          eks = {
            cidr = "10.102.32.0/24"
            name = "saku-${terraform.workspace}-public-1a"
          }
          eks_utility = {
            cidr = "************/27"
            name = "utility-ap-northeast-1a.saku-${terraform.workspace}-k8s.kkstream.tech"
          }
          nat = {
            cidr             = "**********/28"
            name             = "saku-${terraform.workspace}-nat-0"
            route_table_id   = "rtb-03a3fb00b8fa0b92c"
            nat_gateway_name = "saku-${terraform.workspace}-0"
            nat_eip          = "***********"
            nat_eip_name     = "saku-${terraform.workspace}-nat-0"
          }
          playready_elb = {
            cidr = "************/28"
          }
        }
        private = {
          eks = {
            cidr           = "**********/20"
            name           = "saku-${terraform.workspace}-private-1a"
            route_table_id = "rtb-096893d31f760b409"
            nat_gateway_id = "nat-007cfcd6a0154a2ef"
          }
          db = {
            cidr = "***********/28"
            name = "saku-${terraform.workspace}-db-ap-northeast-1a"
          }
          playready = {
            cidr           = "************/28"
            nat_gateway_id = "nat-007cfcd6a0154a2ef"
          }
          coco_lambda = {
            cidr           = "***********/23"
            name           = "ap-northeast-1a.saku-${terraform.workspace}-k8s.kkstream.tech"
            route_table_id = "rtb-03aebfb4e6e66c4b1"
            nat_gateway_id = "nat-007cfcd6a0154a2ef"
          }
          lambda = {
            cidr = "10.101.17.0/28"
            name = "saku-${terraform.workspace}-lambda"
          }
          prometheus = {
            cidr           = "10.101.8.80/28"
            name           = "saku-${terraform.workspace}-prometheus_monitor_subnet"
            route_table_id = "rtb-01a151e47ce3d563b"
            nat_gateway_id = "nat-007cfcd6a0154a2ef"
          }

        }
      }
      "ap-northeast-1c" = {
        public = {
          eks = {
            cidr = "10.102.33.0/24"
            name = "saku-${terraform.workspace}-public-1c"
          }
          eks_utility = {
            cidr = "************/27"
            name = "utility-ap-northeast-1c.saku-${terraform.workspace}-k8s.kkstream.tech"
          }
          nat = {
            cidr             = "***********/28"
            name             = "saku-${terraform.workspace}-nat-1"
            route_table_id   = "rtb-02f6f23076079ec52"
            nat_gateway_name = "saku-${terraform.workspace}-1"
            nat_eip          = "*************"
            nat_eip_name     = "saku-${terraform.workspace}-nat-1"
          }
          playready_elb = {
            cidr = "************/28"
          }
        }
        private = {
          eks = {
            cidr           = "***********/20"
            name           = "saku-${terraform.workspace}-private-1c"
            route_table_id = "rtb-0a691150056254086"
            nat_gateway_id = "nat-0f2e87be696ea3b34"
          }
          db = {
            cidr = "***********/28"
            name = "saku-${terraform.workspace}-db-ap-northeast-1c"
          }
          playready = {
            cidr           = "************/28"
            nat_gateway_id = "nat-0f2e87be696ea3b34"
          }
          coco_lambda = {
            cidr           = "***********/23"
            name           = "ap-northeast-1c.saku-${terraform.workspace}-k8s.kkstream.tech"
            route_table_id = "rtb-09970ceba25df47e7"
            nat_gateway_id = "nat-0f2e87be696ea3b34"
          }
          coco_lambda_1d = {
            cidr           = "10.101.10.0/23"
            name           = "ap-northeast-1d.saku-${terraform.workspace}-k8s.kkstream.tech"
            route_table_id = "rtb-09970ceba25df47e7"
            nat_gateway_id = "nat-0bf0fab6c4ad2651b"
          }
        }
      }
      "ap-northeast-1d" = {
        public = {
          jump = {
            cidr = "10.101.8.64/28"
          }
        }
      }
    }
  }

  default_route_table = {
    prod = {
      cidr                      = "20.2.0.0/16"
      vpc_peering_connection_id = "pcx-0e2f02c124a92e241"
    }
    stag = {
      cidr                      = "20.1.0.0/16"
      vpc_peering_connection_id = "pcx-018101aacf20ad0ca"
    }
  }

  tags = {
    common = {
      Environment = local.env
      Region      = local.aws_region
      Service     = "infrastructure"
      Vendor      = "saku"
      EnvName     = "saku-${terraform.workspace}"
    }
    subnet = {
      eks = {
        "kubernetes.io/cluster/saku-${local.env}-eks" = "owned" # tagged by EKS (https://docs.aws.amazon.com/eks/latest/userguide/network-reqs.html)
      }
    }
    public_subnet = {
      eks = {
        "kubernetes.io/role/elb" = 1 # tagged by EKS (https://docs.aws.amazon.com/eks/latest/userguide/network-load-balancing.html#_prerequisites)
      }
    }
  }
}
