module "vpc" {
  source = "terraform-aws-modules/vpc/aws"


  name = "saku-${terraform.workspace}"
  cidr = local.vpc_cidr[terraform.workspace]

  # eks public subnets
  azs                 = keys(local.subnets[terraform.workspace])
  public_subnets      = [for az, subnets in local.subnets[terraform.workspace] : subnets.public.eks.cidr if lookup(subnets.public, "eks", null) != null]
  public_subnet_names = [for az, subnets in local.subnets[terraform.workspace] : subnets.public.eks.name if lookup(subnets.public, "eks", null) != null]

  manage_default_network_acl    = false
  manage_default_route_table    = false
  manage_default_security_group = false

  tags               = local.tags.common
  public_subnet_tags = merge(local.tags.subnet.eks, local.tags.public_subnet.eks)
}

resource "aws_route_table" "default" {
  vpc_id = module.vpc.vpc_id
  route {
    cidr_block                = local.default_route_table[terraform.workspace].cidr
    vpc_peering_connection_id = local.default_route_table[terraform.workspace].vpc_peering_connection_id
  }

  tags = {
    EnvName = "saku-${terraform.workspace}"
  }
}

# # eks private subnets
resource "aws_subnet" "private_eks" {
  for_each = { for az, subnets in local.subnets[terraform.workspace] : az => subnets.private.eks if lookup(subnets, "private", null) != null }

  vpc_id            = module.vpc.vpc_id
  cidr_block        = each.value.cidr
  availability_zone = each.key
  tags              = merge(local.tags.common, local.tags.subnet.eks, { Name = each.value.name })
}

resource "aws_route_table" "private_eks" {
  for_each = { for az, subnets in local.subnets[terraform.workspace] : az => subnets.private.eks if lookup(subnets, "private", null) != null }

  vpc_id = module.vpc.vpc_id
  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = each.value.nat_gateway_id
  }
  tags = merge(local.tags.common, { Name = each.value.name })
}

resource "aws_route_table_association" "private_eks" {
  for_each = { for az, subnets in local.subnets[terraform.workspace] : az => subnets.private.eks if lookup(subnets, "private", null) != null }

  subnet_id      = aws_subnet.private_eks[each.key].id
  route_table_id = each.value.route_table_id
}

# # nat public subnets
resource "aws_subnet" "nat" {
  for_each = { for az, subnets in local.subnets[terraform.workspace] : az => subnets.public.nat if lookup(subnets.public, "nat", null) != null }

  vpc_id            = module.vpc.vpc_id
  cidr_block        = each.value.cidr
  availability_zone = each.key
  tags              = merge(local.tags.common, { Name = each.value.name })
}

resource "aws_route_table" "nat" {
  for_each = { for az, subnets in local.subnets[terraform.workspace] : az => subnets.public.nat if lookup(subnets.public, "nat", null) != null }

  vpc_id = module.vpc.vpc_id
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = local.internet_gateway_id[terraform.workspace]
  }
  tags = merge(local.tags.common, { Name = each.value.name })
}

resource "aws_route_table_association" "nat" {
  for_each = { for az, subnets in local.subnets[terraform.workspace] : az => subnets.public.nat if lookup(subnets.public, "nat", null) != null }

  subnet_id      = aws_subnet.nat[each.key].id
  route_table_id = aws_route_table.nat[each.key].id
}

resource "aws_eip" "nat" {
  for_each = { for az, subnets in local.subnets[terraform.workspace] : az => subnets.public.nat if lookup(subnets.public, "nat", null) != null }
  tags     = merge(local.tags.common, { Name = each.value.nat_eip_name })
}

resource "aws_nat_gateway" "nat" {
  for_each = { for az, subnets in local.subnets[terraform.workspace] : az => subnets.public.nat if lookup(subnets.public, "nat", null) != null }

  allocation_id = aws_eip.nat[each.key].id
  subnet_id     = aws_subnet.nat[each.key].id
  # kubernetes.io/cluster/saku-stag-k8s.kkstream.tech is part of the legacy
  tags = merge(local.tags.common, { Name = each.value.nat_gateway_name, "kubernetes.io/cluster/saku-stag-k8s.kkstream.tech" = "shared" })
}

# # playready elb public subnets
resource "aws_subnet" "playready_elb" {
  for_each = { for az, subnets in local.subnets[terraform.workspace] : az => subnets.public.playready_elb if lookup(subnets.public, "playready_elb", null) != null }

  vpc_id            = module.vpc.vpc_id
  cidr_block        = each.value.cidr
  availability_zone = each.key
  tags              = { Environment = "saku-${terraform.workspace}", Name = "saku-${terraform.workspace}-service-playready-elb", EnvName = "saku-${terraform.workspace}" }
}

resource "aws_route_table" "playready_elb" {
  vpc_id = module.vpc.vpc_id
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = local.internet_gateway_id[terraform.workspace]
  }
  tags = { Environment = "saku-${terraform.workspace}", Name = "saku-${terraform.workspace}-service-playready", EnvName = "saku-${terraform.workspace}" }
}

resource "aws_route_table_association" "playready_elb" {
  for_each = { for az, subnets in local.subnets[terraform.workspace] : az => subnets.public.playready_elb if lookup(subnets.public, "playready_elb", null) != null }

  subnet_id      = aws_subnet.playready_elb[each.key].id
  route_table_id = aws_route_table.playready_elb.id
}

# # EKS utility public subnets
resource "aws_subnet" "eks_utility" {
  for_each = { for az, subnets in local.subnets[terraform.workspace] : az => subnets.public.eks_utility if lookup(subnets.public, "eks_utility", null) != null }

  vpc_id            = module.vpc.vpc_id
  cidr_block        = each.value.cidr
  availability_zone = each.key
  tags              = { EnvName = "saku-${terraform.workspace}", Name = "utility-${each.key}.saku-${terraform.workspace}-k8s.kkstream.tech", SubnetType = "Utility", KubernetesCluster = "saku-${terraform.workspace}-k8s.kkstream.tech", "kubernetes.io/cluster/saku-${terraform.workspace}-k8s.kkstream.tech" = "owned", "kubernetes.io/role/elb" = 1 }
}

resource "aws_route_table" "eks_utility" {
  vpc_id = module.vpc.vpc_id
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = local.internet_gateway_id[terraform.workspace]
  }
  tags = { EnvName = "saku-${terraform.workspace}", Name = "saku-${terraform.workspace}-k8s.kkstream.tech", KubernetesCluster = "saku-${terraform.workspace}-k8s.kkstream.tech", "kubernetes.io/cluster/saku-${terraform.workspace}-k8s.kkstream.tech" = "owned", "kubernetes.io/kops/role" = "public" }
}

resource "aws_route_table_association" "eks_utility" {
  for_each = { for az, subnets in local.subnets[terraform.workspace] : az => subnets.public.eks_utility if lookup(subnets.public, "eks_utility", null) != null }

  subnet_id      = aws_subnet.eks_utility[each.key].id
  route_table_id = aws_route_table.eks_utility.id
}

# # coco-maintenance-catchup-programs-info-fetcher-backend (abbr coco lambda )private subnets
resource "aws_subnet" "coco_lambda" {
  for_each = { for az, subnets in local.subnets[terraform.workspace] : az => subnets.private.coco_lambda if lookup(subnets, "private", null) != null }

  vpc_id            = module.vpc.vpc_id
  cidr_block        = each.value.cidr
  availability_zone = each.key
  tags              = { EnvName = "saku-${terraform.workspace}", Name = "${each.key}.saku-${terraform.workspace}-k8s.kkstream.tech", SubnetType = "Private", KubernetesCluster = "saku-${terraform.workspace}-k8s.kkstream.tech", "kubernetes.io/cluster/saku-${terraform.workspace}-k8s.kkstream.tech" = "owned", "kubernetes.io/role/internal-elb" = 1 }
}

resource "aws_route_table" "coco_lambda" {
  for_each = { for az, subnets in local.subnets[terraform.workspace] : az => subnets.private.coco_lambda if lookup(subnets, "private", null) != null }

  vpc_id = module.vpc.vpc_id
  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = each.value.nat_gateway_id
  }

  tags = { EnvName = "saku-${terraform.workspace}", Name = "private-${each.key}.saku-${terraform.workspace}-k8s.kkstream.tech", KubernetesCluster = "saku-${terraform.workspace}-k8s.kkstream.tech", "kubernetes.io/cluster/saku-${terraform.workspace}-k8s.kkstream.tech" = "owned", "kubernetes.io/kops/role" = "private-${each.key}" }
}

resource "aws_route_table_association" "coco_lambda" {
  for_each = { for az, subnets in local.subnets[terraform.workspace] : az => subnets.private.coco_lambda if lookup(subnets, "private", null) != null }

  subnet_id      = aws_subnet.coco_lambda[each.key].id
  route_table_id = aws_route_table.coco_lambda[each.key].id
}

# # there is a coco lambda subnet named by 1d but it is actually in 1c
resource "aws_subnet" "coco_lambda_1d" {
  vpc_id            = module.vpc.vpc_id
  cidr_block        = local.subnets[terraform.workspace]["ap-northeast-1c"].private.coco_lambda_1d.cidr
  availability_zone = "ap-northeast-1c"
  tags              = { EnvName = "saku-${terraform.workspace}", Name = "ap-northeast-1d.saku-${terraform.workspace}-k8s.kkstream.tech", SubnetType = "Private", KubernetesCluster = "saku-${terraform.workspace}-k8s.kkstream.tech", "kubernetes.io/cluster/saku-${terraform.workspace}-k8s.kkstream.tech" = "owned", "kubernetes.io/role/internal-elb" = 1 }
}

resource "aws_route_table_association" "coco_lambda_1d" {
  subnet_id      = aws_subnet.coco_lambda_1d.id
  route_table_id = aws_route_table.coco_lambda["ap-northeast-1c"].id
}

# # db private subnets
resource "aws_subnet" "db" {
  for_each = { for az, subnets in local.subnets[terraform.workspace] : az => subnets.private.db if lookup(subnets, "private", null) != null }

  vpc_id            = module.vpc.vpc_id
  cidr_block        = each.value.cidr
  availability_zone = each.key
  tags              = { EnvName = "saku-${terraform.workspace}", Environment = "saku-${terraform.workspace}", Name = each.value.name }
}

resource "aws_subnet" "playready" {
  for_each = { for az, subnets in local.subnets[terraform.workspace] : az => subnets.private.playready if lookup(subnets, "private", null) != null }

  vpc_id            = module.vpc.vpc_id
  cidr_block        = each.value.cidr
  availability_zone = each.key
  tags              = { EnvName = "saku-${terraform.workspace}", Environment = "saku-${terraform.workspace}", Name = "saku-${terraform.workspace}-service-playready" }
}

resource "aws_route_table" "playready" {
  for_each = { for az, subnets in local.subnets[terraform.workspace] : az => subnets.private.playready if lookup(subnets, "private", null) != null }

  vpc_id = module.vpc.vpc_id
  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = each.value.nat_gateway_id
  }
  tags = { EnvName = "saku-${terraform.workspace}", Environment = "saku-${terraform.workspace}", Name = "saku-${terraform.workspace}-service-playready" }
}

resource "aws_route_table_association" "playready" {
  for_each = { for az, subnets in local.subnets[terraform.workspace] : az => subnets.private.playready if lookup(subnets, "private", null) != null }

  subnet_id      = aws_subnet.playready[each.key].id
  route_table_id = aws_route_table.playready[each.key].id
}

# # lambda subnet
resource "aws_subnet" "lambda" {
  count             = terraform.workspace == "prod" ? 1 : 0
  vpc_id            = module.vpc.vpc_id
  cidr_block        = local.subnets[terraform.workspace]["ap-northeast-1a"].private.lambda.cidr
  availability_zone = "ap-northeast-1a"
  tags              = { EnvName = "saku-${terraform.workspace}", Name = local.subnets[terraform.workspace]["ap-northeast-1a"].private.lambda.name }
}

# prometheus private subnet
resource "aws_subnet" "prometheus" {
  vpc_id            = module.vpc.vpc_id
  cidr_block        = local.subnets[terraform.workspace]["ap-northeast-1a"].private.prometheus.cidr
  availability_zone = "ap-northeast-1a"
  tags              = { EnvName = "saku-${terraform.workspace}", Name = local.subnets[terraform.workspace]["ap-northeast-1a"].private.prometheus.name }
}

resource "aws_route_table" "prometheus" {
  vpc_id = module.vpc.vpc_id
  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = local.subnets[terraform.workspace]["ap-northeast-1a"].private.prometheus.nat_gateway_id
  }
  tags = { EnvName = "saku-${terraform.workspace}", Name = "saku-${terraform.workspace}-prometheus-monitor" }
}

resource "aws_route_table_association" "prometheus" {
  subnet_id      = aws_subnet.prometheus.id
  route_table_id = aws_route_table.prometheus.id
}

# # jump subnet in ap-northeast-1d
resource "aws_subnet" "jump" {
  vpc_id            = module.vpc.vpc_id
  cidr_block        = local.subnets[terraform.workspace]["ap-northeast-1d"].public.jump.cidr
  availability_zone = "ap-northeast-1d"
  tags              = { EnvName = "saku-${terraform.workspace}", Name = "saku-${terraform.workspace}-jump-subnet" }
}

resource "aws_route_table" "jump" {
  vpc_id = module.vpc.vpc_id
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = local.internet_gateway_id[terraform.workspace]
  }

  tags = { EnvName = "saku-${terraform.workspace}", Environment = "saku-${terraform.workspace}", Name = "saku-${terraform.workspace}-ingress" }
}

resource "aws_route_table_association" "jump" {
  subnet_id      = aws_subnet.jump.id
  route_table_id = aws_route_table.jump.id
}

resource "aws_vpc_endpoint" "s3" {
  vpc_id       = module.vpc.vpc_id
  service_name = "com.amazonaws.ap-northeast-1.s3"
  route_table_ids = [
    aws_route_table.private_eks["ap-northeast-1a"].id,
    aws_route_table.private_eks["ap-northeast-1c"].id,
    aws_route_table.eks_utility.id,
    aws_route_table.coco_lambda["ap-northeast-1a"].id,
    aws_route_table.coco_lambda["ap-northeast-1c"].id,
  ]

  tags = local.tags.common
}
