resource "aws_eks_cluster" "this" {
  name     = local.name
  version  = local.versions[terraform.workspace].cluster
  role_arn = data.aws_iam_role.saku_eks_master.arn

  bootstrap_self_managed_addons = false

  access_config {
    authentication_mode                         = "CONFIG_MAP"
    bootstrap_cluster_creator_admin_permissions = true
  }

  encryption_config {
    resources = ["secrets"]
    provider {
      key_arn = local.key_arn[terraform.workspace]
    }
  }
  vpc_config {
    subnet_ids              = [for az, subnet in data.aws_subnet.subnets : subnet.id]
    endpoint_private_access = true
    security_group_ids      = local.vpc_config_security_group_ids[terraform.workspace]
  }

  enabled_cluster_log_types = [
    "api",
    "audit",
    "authenticator"
  ]

  tags = merge(local.tags.common, { "aws.service" = "eks", "tf.module" = "eks" })
}

resource "aws_eks_node_group" "general" {
  cluster_name    = aws_eks_cluster.this.name
  node_group_name = "general"
  node_role_arn   = data.aws_iam_role.saku_eks_node.arn
  subnet_ids      = [for az, subnet in data.aws_subnet.subnets : subnet.id if strcontains(subnet.tags.Name, "private")]
  ami_type        = "AL2_x86_64"

  instance_types = ["t3.medium"]
  labels = {
    "service" = "general"
    "subnet"  = "private"
  }

  scaling_config {
    desired_size = local.node_group.scaling_config.general[terraform.workspace].desired_size
    max_size     = local.node_group.scaling_config.general[terraform.workspace].max_size
    min_size     = local.node_group.scaling_config.general[terraform.workspace].min_size
  }

  update_config {
    max_unavailable_percentage = 30
  }

  remote_access {
    ec2_ssh_key               = local.name
    source_security_group_ids = local.node_group_source_security_group_ids[terraform.workspace]
  }

  tags = merge(local.tags.common, { "aws.service" = "eks.node", "tf.module" = "eks" })
}

resource "aws_eks_node_group" "jon" {
  cluster_name    = aws_eks_cluster.this.name
  node_group_name = "jon"
  node_role_arn   = data.aws_iam_role.saku_eks_node.arn
  subnet_ids      = [for az, subnet in data.aws_subnet.subnets : subnet.id if strcontains(subnet.tags.Name, "private")]
  ami_type        = "AL2_x86_64"

  instance_types = ["t3.medium"]
  labels = {
    "service" = "jon"
    "subnet"  = "private"
  }

  taint {
    key    = "jon"
    value  = "true"
    effect = "NO_SCHEDULE"
  }

  scaling_config {
    desired_size = 0
    max_size     = 20
    min_size     = 0
  }

  update_config {
    max_unavailable_percentage = 30
  }

  remote_access {
    ec2_ssh_key               = local.name
    source_security_group_ids = local.node_group_source_security_group_ids[terraform.workspace]
  }

  tags = merge(local.tags.common, { "aws.service" = "eks.node", "tf.module" = "eks" })
}
resource "aws_eks_node_group" "coco" {
  cluster_name    = aws_eks_cluster.this.name
  node_group_name = "coco"
  node_role_arn   = data.aws_iam_role.saku_eks_node.arn
  subnet_ids      = [for az, subnet in data.aws_subnet.subnets : subnet.id if strcontains(subnet.tags.Name, "private")]
  ami_type        = "AL2_x86_64"

  instance_types = local.node_group.instance_types.coco[terraform.workspace]
  labels = {
    "service" = "coco"
    "subnet"  = "private"
  }

  taint {
    key    = "coco"
    value  = "true"
    effect = "NO_SCHEDULE"
  }

  scaling_config {
    desired_size = local.node_group.scaling_config.coco[terraform.workspace].desired_size
    max_size     = local.node_group.scaling_config.coco[terraform.workspace].max_size
    min_size     = local.node_group.scaling_config.coco[terraform.workspace].min_size
  }

  update_config {
    max_unavailable_percentage = 30
  }

  remote_access {
    ec2_ssh_key               = local.name
    source_security_group_ids = local.node_group_source_security_group_ids[terraform.workspace]
  }

  tags = merge(local.tags.common, { Service = "coco", "aws.service" = "eks.node", "tf.module" = "eks" })
}

resource "aws_eks_node_group" "monitoring" {
  cluster_name    = aws_eks_cluster.this.name
  node_group_name = "monitoring"
  node_role_arn   = data.aws_iam_role.saku_eks_node.arn
  subnet_ids      = [for az, subnet in data.aws_subnet.subnets : subnet.id if strcontains(subnet.tags.Name, "private")]
  ami_type        = "AL2_x86_64"

  instance_types = [
    "m5.xlarge",
    "m5a.xlarge",
    "m6a.xlarge",
    "m6i.xlarge"
  ]
  labels = {
    "service" = "monitoring"
    "subnet"  = "private"
  }

  taint {
    key    = "monitoring"
    value  = "true"
    effect = "NO_SCHEDULE"
  }

  scaling_config {
    desired_size = local.node_group.scaling_config.monitoring[terraform.workspace].desired_size
    max_size     = local.node_group.scaling_config.monitoring[terraform.workspace].max_size
    min_size     = local.node_group.scaling_config.monitoring[terraform.workspace].min_size
  }

  update_config {
    max_unavailable_percentage = 30
  }

  remote_access {
    ec2_ssh_key               = local.name
    source_security_group_ids = local.node_group_source_security_group_ids[terraform.workspace]
  }

  tags = merge(local.tags.common, { Service = "monitoring", "aws.service" = "eks.node", "tf.module" = "eks" })
}

resource "aws_eks_node_group" "luke_normal" {
  cluster_name    = aws_eks_cluster.this.name
  node_group_name = "luke_normal"
  node_role_arn   = data.aws_iam_role.saku_eks_node.arn
  subnet_ids      = [for az, subnet in data.aws_subnet.subnets : subnet.id if strcontains(subnet.tags.Name, "private")]
  ami_type        = "AL2_x86_64"

  instance_types = ["c5.9xlarge"]
  labels = {
    "service"  = "luke"
    "subnet"   = "private"
    "queue"    = "normal"
    "priority" = "primary"
  }

  taint {
    effect = "NO_SCHEDULE"
    key    = "luke"
    value  = "true"
  }

  scaling_config {
    desired_size = 0
    max_size     = 100
    min_size     = 0
  }

  update_config {
    max_unavailable_percentage = 30
  }

  remote_access {
    ec2_ssh_key               = local.name
    source_security_group_ids = local.node_group_source_security_group_ids[terraform.workspace]
  }

  tags = merge(local.tags.common, { "aws.service" = "eks.node", "tf.module" = "eks", "Service" = "luke", "Queue" = "normal" })
}

resource "aws_eks_node_group" "luke_normal_secondary" {
  cluster_name    = aws_eks_cluster.this.name
  node_group_name = "luke_normal_secondary"
  node_role_arn   = data.aws_iam_role.saku_eks_node.arn
  subnet_ids      = [for az, subnet in data.aws_subnet.subnets : subnet.id if strcontains(subnet.tags.Name, "private")]
  ami_type        = "AL2_x86_64"

  instance_types = ["c5a.4xlarge",
    "c6a.4xlarge",
    "c6i.4xlarge",
    "c5.4xlarge"
  ]
  labels = {
    "service"  = "luke"
    "subnet"   = "private"
    "queue"    = "normal"
    "priority" = "secondary"
  }

  taint {
    effect = "NO_SCHEDULE"
    key    = "luke"
    value  = "true"
  }

  scaling_config {
    desired_size = 0
    max_size     = 100
    min_size     = 0
  }

  update_config {
    max_unavailable_percentage = 30
  }

  remote_access {
    ec2_ssh_key               = local.name
    source_security_group_ids = local.node_group_source_security_group_ids[terraform.workspace]
  }

  tags = merge(local.tags.common, { "aws.service" = "eks.node", "tf.module" = "eks", "Service" = "luke", "Queue" = "normal" })
}

resource "aws_eks_node_group" "luke_premium" {
  cluster_name    = aws_eks_cluster.this.name
  node_group_name = "luke_premium"
  node_role_arn   = data.aws_iam_role.saku_eks_node.arn
  subnet_ids      = [for az, subnet in data.aws_subnet.subnets : subnet.id if strcontains(subnet.tags.Name, "private")]
  ami_type        = "AL2_x86_64"

  instance_types = ["c5.9xlarge"]
  labels = {
    "service"  = "luke"
    "subnet"   = "private"
    "queue"    = "premium"
    "priority" = "primary"
  }

  taint {
    effect = "NO_SCHEDULE"
    key    = "luke"
    value  = "true"
  }

  scaling_config {
    desired_size = 0
    max_size     = 100
    min_size     = 0
  }

  update_config {
    max_unavailable_percentage = 30
  }

  remote_access {
    ec2_ssh_key               = local.name
    source_security_group_ids = local.node_group_source_security_group_ids[terraform.workspace]
  }

  tags = merge(local.tags.common, { "aws.service" = "eks.node", "tf.module" = "eks", "Service" = "luke", "Queue" = "premium" })
}

resource "aws_eks_node_group" "luke_premium_secondary" {
  cluster_name    = aws_eks_cluster.this.name
  node_group_name = "luke_premium_secondary"
  node_role_arn   = data.aws_iam_role.saku_eks_node.arn
  subnet_ids      = [for az, subnet in data.aws_subnet.subnets : subnet.id if strcontains(subnet.tags.Name, "private")]
  ami_type        = "AL2_x86_64"

  instance_types = [
    "c5a.4xlarge",
    "c6a.4xlarge",
    "c6i.4xlarge",
    "c5.4xlarge",
  ]
  labels = {
    "service"  = "luke"
    "subnet"   = "private"
    "queue"    = "premium"
    "priority" = "secondary"
  }

  taint {
    effect = "NO_SCHEDULE"
    key    = "luke"
    value  = "true"
  }

  scaling_config {
    desired_size = 0
    max_size     = 100
    min_size     = 0
  }

  update_config {
    max_unavailable_percentage = 30
  }

  remote_access {
    ec2_ssh_key               = local.name
    source_security_group_ids = local.node_group_source_security_group_ids[terraform.workspace]
  }

  tags = merge(local.tags.common, { "aws.service" = "eks.node", "tf.module" = "eks", "Service" = "luke", "Queue" = "premium" })
}

resource "aws_eks_node_group" "luke_priority" {
  cluster_name    = aws_eks_cluster.this.name
  node_group_name = "luke_priority"
  node_role_arn   = data.aws_iam_role.saku_eks_node.arn
  subnet_ids      = [for az, subnet in data.aws_subnet.subnets : subnet.id if strcontains(subnet.tags.Name, "private")]
  ami_type        = "AL2_x86_64"

  instance_types = ["c5.9xlarge"]
  labels = {
    "service"  = "luke"
    "subnet"   = "private"
    "queue"    = "priority"
    "priority" = "primary"
  }

  taint {
    effect = "NO_SCHEDULE"
    key    = "luke"
    value  = "true"
  }

  scaling_config {
    desired_size = local.node_group.scaling_config.luke_priority[terraform.workspace].desired_size
    max_size     = local.node_group.scaling_config.luke_priority[terraform.workspace].max_size
    min_size     = local.node_group.scaling_config.luke_priority[terraform.workspace].min_size
  }

  update_config {
    max_unavailable_percentage = 30
  }

  remote_access {
    ec2_ssh_key               = local.name
    source_security_group_ids = local.node_group_source_security_group_ids[terraform.workspace]
  }

  tags = merge(local.tags.common, { "aws.service" = "eks.node", "tf.module" = "eks", "Service" = "luke", "Queue" = "priority" })
}

resource "aws_eks_node_group" "luke_priority_secondary" {
  cluster_name    = aws_eks_cluster.this.name
  node_group_name = "luke_priority_secondary"
  node_role_arn   = data.aws_iam_role.saku_eks_node.arn
  subnet_ids      = [for az, subnet in data.aws_subnet.subnets : subnet.id if strcontains(subnet.tags.Name, "private")]
  ami_type        = "AL2_x86_64"

  instance_types = [
    "c5a.4xlarge",
    "c6a.4xlarge",
    "c6i.4xlarge",
    "c5.4xlarge",
  ]
  labels = {
    "service"  = "luke"
    "subnet"   = "private"
    "queue"    = "priority"
    "priority" = "secondary"
  }

  taint {
    effect = "NO_SCHEDULE"
    key    = "luke"
    value  = "true"
  }

  scaling_config {
    desired_size = 0
    max_size     = 100
    min_size     = 0
  }

  update_config {
    max_unavailable_percentage = 30
  }

  remote_access {
    ec2_ssh_key               = local.name
    source_security_group_ids = local.node_group_source_security_group_ids[terraform.workspace]
  }

  tags = merge(local.tags.common, { "aws.service" = "eks.node", "tf.module" = "eks", "Service" = "luke", "Queue" = "priority" })
}

resource "aws_eks_addon" "addons" {
  for_each      = tomap({ for k, v in local.versions[terraform.workspace].addons : k => v if k != "amazon-cloudwatch-observability" })
  cluster_name  = aws_eks_cluster.this.name
  addon_name    = each.key
  addon_version = each.value

  tags = merge(local.tags.common, { "aws.service" = "eks.${each.key}" })
}

resource "aws_eks_addon" "cloudwatch-observability" {
  count                    = try(local.versions[terraform.workspace].addons["amazon-cloudwatch-observability"], null) != null ? 1 : 0
  cluster_name             = aws_eks_cluster.this.name
  addon_name               = "amazon-cloudwatch-observability"
  addon_version            = try(local.versions[terraform.workspace].addons["amazon-cloudwatch-observability"], null)
  service_account_role_arn = data.aws_iam_role.cloudwatch_observability_addon[count.index].arn

  tags = { EnvName = "saku-${terraform.workspace}" }
}
