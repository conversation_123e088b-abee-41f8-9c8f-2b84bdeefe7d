locals {
  aws_region  = "ap-northeast-1"
  env         = terraform.workspace == "stag" ? "stage" : terraform.workspace
  network_env = contains(["stag", "prep"], terraform.workspace) ? "stag" : terraform.workspace
  name        = "saku-${local.env}-eks"
  versions = {
    prod = {
      cluster = "1.30"
      addons = {
        vpc-cni            = "v1.19.0-eksbuild.1"
        aws-ebs-csi-driver = "v1.39.0-eksbuild.1"
        coredns            = "v1.11.1-eksbuild.8"
        kube-proxy         = "v1.30.6-eksbuild.3"
      }
    }
    prep = {
      cluster = "1.31"
      addons = {
        vpc-cni            = "v1.19.2-eksbuild.1"
        aws-ebs-csi-driver = "v1.38.1-eksbuild.1"
        coredns            = "v1.11.4-eksbuild.1"
        kube-proxy         = "v1.29.1-eksbuild.2"
        amazon-cloudwatch-observability = "v3.6.0-eksbuild.2"
      }
    }
    stag = {
      cluster = "1.31"
      addons = {
        vpc-cni                         = "v1.19.2-eksbuild.1"
        aws-ebs-csi-driver              = "v1.38.1-eksbuild.1"
        coredns                         = "v1.11.4-eksbuild.1"
        kube-proxy                      = "v1.31.2-eksbuild.3"
        amazon-cloudwatch-observability = "v3.6.0-eksbuild.2"
      }
    }
  }

  vpc_config_security_group_ids = {
    prod = ["sg-08df2bd7fee73b233"]
    prep = ["sg-00022127a592aecae"]
    stag = ["sg-0c7719bdf37fd18ca"]
  }
  key_arn = {
    prod = "arn:aws:kms:ap-northeast-1:138046196805:key/f8242dcb-3fee-45e9-b2fd-c78681ad9fd7"
    prep = "arn:aws:kms:ap-northeast-1:138046196805:key/5c6f1e00-831d-49e5-8138-e585536b4029"
    stag = "arn:aws:kms:ap-northeast-1:138046196805:key/33aa9d91-521f-457b-9618-5568adb60b0b"
  }

  node_group_source_security_group_ids = {
    prod = ["sg-07d8a5aed700bd61b", "sg-0a4fa051d0d2078d5"]
    prep = ["sg-001f23bf530320c9c", "sg-0d4c5cac4335f7fa4"]
    stag = ["sg-0a69af7d2ba3493b7", "sg-0d58642355617fb4d"]
  }

  node_group = {
    ami_type = {
      prod = "AL2_x86_64"
      prep = "AL2_x86_64"
      stag = "AL2023_x86_64_STANDARD"
    }
    instance_types = {
      coco = {
        prod = [
          "t3.xlarge",
          "c5.xlarge",
          "c5a.xlarge",
          "c6a.xlarge",
          "c6i.xlarge",
          "m5.xlarge",
          "m5a.xlarge",
          "m6a.xlarge",
          "m6i.xlarge"
        ]
        prep = [
          "m5.xlarge",
          "m5a.xlarge",
          "m6a.xlarge",
          "m6i.xlarge",
        ]
        stag = [
          "t3.xlarge",
          "c5.xlarge",
          "c5a.xlarge",
          "c6a.xlarge",
          "c6i.xlarge",
          "m5.xlarge",
          "m5a.xlarge",
          "m6a.xlarge",
          "m6i.xlarge"
        ]
      }
    }
    scaling_config = {
      general = {
        prod = {
          desired_size = 14
          max_size     = 30
          min_size     = 1
        }
        prep = {
          desired_size = 14
          max_size     = 30
          min_size     = 14
        }
        stag = {
          desired_size = 12
          max_size     = 30
          min_size     = 1
        }
      }

      coco = {
        prod = {
          desired_size = 1
          max_size     = 10
          min_size     = 1
        }
        prep = {
          desired_size = 0
          max_size     = 10
          min_size     = 0
        }
        stag = {
          desired_size = 1
          max_size     = 10
          min_size     = 0
        }
      }
      luke_priority = {
        prod = {
          desired_size = 3
          max_size     = 100
          min_size     = 0
        }
        prep = {
          desired_size = 0
          max_size     = 100
          min_size     = 0
        }
        stag = {
          desired_size = 0
          max_size     = 100
          min_size     = 0
        }
      }
      monitoring = {
        prod = {
          desired_size = 2
          max_size     = 10
          min_size     = 2
        }
        prep = {
          desired_size = 1
          max_size     = 10
          min_size     = 0
        }
        stag = {
          desired_size = 1
          max_size     = 10
          min_size     = 0
        }
      }
    }
  }

  tags = {
    common = {
      Environment = local.env
      Region      = local.aws_region
      Vendor      = "saku"
      EnvName     = "saku-${terraform.workspace}"
    }
  }
}
