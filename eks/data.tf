data "aws_vpc" "vpc" {
  tags = {
    Name = "saku-${local.network_env}"
  }
}

# data for private subnets
data "aws_subnet" "subnets" {
  for_each = toset([
    "saku-${local.network_env}-public-1a",
    "saku-${local.network_env}-public-1c",
    "saku-${local.network_env}-private-1a",
    "saku-${local.network_env}-private-1c",
  ])

  vpc_id = data.aws_vpc.vpc.id
  tags = {
    Name = each.value
  }
}
data "aws_iam_role" "saku_eks_master" {
  name = "saku-${terraform.workspace == "stag" ? "stage" : terraform.workspace}-eks-master"
}

data "aws_iam_role" "saku_eks_node" {
  name = "saku-${terraform.workspace == "stag" ? "stage" : terraform.workspace}-eks-node"
}

data "aws_iam_role" "cloudwatch_observability_addon" {
  count = terraform.workspace == "stag" ? 1 : 0
  name  = "cloudwatch-observability-saku-${local.env}"
}
output "vpc_id" {
  value = data.aws_vpc.vpc.id
}

output "subnets" {
  value = data.aws_subnet.subnets
}
