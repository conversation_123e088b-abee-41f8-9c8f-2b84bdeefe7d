
resource "aws_ecs_cluster" "saku_coco" {
  name = "saku-coco-${terraform.workspace}"
  tags = merge(local.tags.common, { Name = "saku-coco-${terraform.workspace}-ecscluster" })
}
resource "aws_ecs_service" "saku_coco_saku_module_daas_consumer_mysql" {
  name            = "saku-coco-${terraform.workspace}-saku-module-daas-consumer-mysql"
  cluster         = aws_ecs_cluster.saku_coco.id
  task_definition = "${aws_ecs_task_definition.saku_coco_saku_module_daas_consumer_mysql.id}:${aws_ecs_task_definition.saku_coco_saku_module_daas_consumer_mysql.revision}"
  desired_count   = 1
  launch_type     = "FARGATE"

  network_configuration {
    assign_public_ip = false
    security_groups  = local.network_configuration.security_groups[terraform.workspace]
    subnets          = local.network_configuration.subnets[terraform.workspace]
  }

  propagate_tags = "SERVICE"
  tags           = merge(local.tags.common, { Name = "saku-coco-${terraform.workspace}-eservice-saku-module-daas-consumer-mysql" })
}
resource "aws_ecs_task_definition" "saku_coco_saku_module_daas_consumer_mysql" {
  family                 = "saku-coco-${terraform.workspace}-saku-module-daas-consumer-mysql"
  enable_fault_injection = false

  container_definitions    = jsonencode(local.container_definitions[terraform.workspace])
  requires_compatibilities = ["FARGATE"]
  cpu                      = 512
  memory                   = 1024
  network_mode             = "awsvpc"
  execution_role_arn       = data.aws_iam_role.saku_coco_execution_saku_module_daas_consumer_mysql.arn
  task_role_arn            = data.aws_iam_role.saku_coco_service_saku_module_daas_consumer_mysql.arn
  tags                     = merge(local.tags.common, { Name = "saku-coco-${terraform.workspace}-etaskdef-saku-module-daas-consumer-mysql" })
}
