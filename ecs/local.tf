locals {
  aws_region = "ap-northeast-1"
  env        = terraform.workspace == "stag" ? "stage" : terraform.workspace

  network_configuration = {
    subnets = {
      prod = [
        "subnet-036fa947db8e67953",
        "subnet-0f4c9da694dd01bd5",
        "subnet-0f773c1b1acf6e25c",
      ]
      prep = [
        "subnet-023e6f06924de4e61",
        "subnet-04921beb481cf96e3",
        "subnet-05e6c5415e599714e",
      ]
      stag = [
        "subnet-023e6f06924de4e61",
        "subnet-04921beb481cf96e3",
        "subnet-05e6c5415e599714e",
      ]
    }
    security_groups = {
      prod = [
        "sg-08db3a78f58dc5eef",
        "sg-09f7432f5191df219",
      ],
      prep = [
        "sg-021687681bd9bc2ec",
        "sg-02fb57215b38af34c",
      ]
      stag = [
        "sg-03b725e56b557518b",
        "sg-0ddddd4d6d5fc6237",
      ]
    }
  }

  container_definitions = {
    prod = [
      {
        name      = "saku-module-daas-consumer-mysql"
        image     = "138046196805.dkr.ecr.ap-northeast-1.amazonaws.com/saku-coco-prod-saku-module-daas-consumer:latest"
        cpu       = 0
        essential = true
        portMappings = [
          {
            containerPort = 8080
            hostPort      = 8080
            name          = "saku-module-daas-consumer-mysql-8080-tcp"
            protocol      = "tcp"
          }
        ]

        environment = [
          {
            name  = "DB_ENDPOINT"
            value = "saku-prod-db.cluster-ci4jygjfmibd.ap-northeast-1.rds.amazonaws.com"
          },
          {
            name  = "DB_ENGINE"
            value = "mysql"
          },
          {
            name  = "DB_NAME"
            value = "jcl"
          },
          {
            name  = "DB_TABLE_LIVETOVOD"
            value = "live_to_vod"
          },
          {
            name  = "DB_TABLE_PROGRAM_INFO"
            value = "program_info"
          },
          {
            name  = "DDB_LOCK_TABLE"
            value = "saku-coco-prod-saku-module-program-lock-mysql-consumer"
          },
          {
            name  = "ENV"
            value = "prod"
          },
          {
            name  = "ENVIRONMENT"
            value = "prod"
          },
          {
            name = "KAFKA_BROKER_URLS"
            value = jsonencode(
              [
                "b-1.daas-prod.kafka:9092",
                "b-2.daas-prod.kafka:9092",
                "b-3.daas-prod.kafka:9092",
              ]
            )
          },
          {
            name  = "KAFKA_CONSUMER_GROUP_ID"
            value = "coco-saku-module-daas-consumer-MySQL-prod"
          },
          {
            name  = "KAFKA_PROGRAM_INFO_TOPIC_NAME"
            value = "jcom-saku-meta-notification"
          },
          {
            name  = "PARAMETERS_CHANNEL_CONVERT_NAME"
            value = "saku-coco-prod-saku-module-channel-convert"
          },
          {
            name  = "PARAMETERS_CHANNEL_ID_LIST_NAME"
            value = "saku-coco-prod-saku-module-channel-id"
          },
          {
            name  = "QUEUE_CATCH_UP_URL"
            value = "https://sqs.ap-northeast-1.amazonaws.com/138046196805/saku-prod-catchup"
          },
          {
            name  = "SENTRY_DSN"
            value = "https://<EMAIL>/5938405"
          },
          {
            name  = "SNS_ERROR_NOTIFICATION_NAME"
            value = "saku-coco-prod-error-notification"
          },
          {
            name  = "VENDOR_ID"
            value = ""
          },
          {
            name  = "VENDOR_TOKEN"
            value = "saku"
          },
        ]

        secrets = [
          {
            name      = "DB_PASSWORD"
            valueFrom = "arn:aws:secretsmanager:ap-northeast-1:138046196805:secret:saku-prod-db20210706082713817800000001-ohwHLH:password::"
          },
          {
            name      = "DB_USERNAME"
            valueFrom = "arn:aws:secretsmanager:ap-northeast-1:138046196805:secret:saku-prod-db20210706082713817800000001-ohwHLH:username::"
          }
        ]

        logConfiguration = {
          logDriver = "awslogs"
          options = {
            "awslogs-group"         = "saku-coco-prod-ecs-saku-module-daas-consumer-mysql"
            "awslogs-region"        = "ap-northeast-1"
            "awslogs-stream-prefix" = "saku-module-daas-consumer-mysql"
          }
        }

        mountPoints = []
        volumesFrom = []

        readonlyRootFilesystem = true

        ulimits = [
          {
            name      = "nofile"
            softLimit = 10000
            hardLimit = 10000
          }
        ]
      }
    ],

    prep = [{
      environment = [
        {
          name  = "BLOCK_HISTORY_ENV"
          value = "pre-prod"
        },
        {
          name  = "DB_ENDPOINT"
          value = "saku-stag-pre-prod-db.cluster-ci4jygjfmibd.ap-northeast-1.rds.amazonaws.com"
        },
        {
          name  = "DB_ENGINE"
          value = "mysql"
        },
        {
          name  = "DB_NAME"
          value = "jcl"
        },
        {
          name  = "DB_TABLE_LIVETOVOD"
          value = "live_to_vod"
        },
        {
          name  = "DB_TABLE_PROGRAM_INFO"
          value = "program_info"
        },
        {
          name  = "DDB_LOCK_TABLE"
          value = "saku-coco-prep-saku-module-program-lock-mysql-consumer"
        },
        {
          name  = "ENV"
          value = "prep"
        },
        {
          name  = "ENVIRONMENT"
          value = "prep"
        },
        {
          name = "KAFKA_BROKER_URLS"
          value = jsonencode(
            [
              "b-1.daas-beta.kafka:9092",
              "b-2.daas-beta.kafka:9092",
              "b-3.daas-beta.kafka:9092",
            ]
          )
        },
        {
          name  = "KAFKA_CONSUMER_GROUP_ID"
          value = "coco-saku-module-daas-consumer-MySQL-prep"
        },
        {
          name  = "KAFKA_PROGRAM_INFO_TOPIC_NAME"
          value = "jcom-saku-prep-meta-notification"
        },
        {
          name  = "PARAMETERS_CHANNEL_CONVERT_NAME"
          value = "saku-coco-prep-saku-module-channel-convert"
        },
        {
          name  = "PARAMETERS_CHANNEL_ID_LIST_NAME"
          value = "saku-coco-prep-saku-module-channel-id"
        },
        {
          name  = "QUEUE_CATCH_UP_URL"
          value = "https://sqs.ap-northeast-1.amazonaws.com/138046196805/saku-preprod-catchup"
        },
        {
          name  = "SENTRY_DSN"
          value = "https://<EMAIL>/5938405"
        },
        {
          name  = "SNS_ERROR_NOTIFICATION_NAME"
          value = "saku-coco-prep-error-notification"
        },
        {
          name  = "VENDOR_ID"
          value = ""
        },
        {
          name  = "VENDOR_TOKEN"
          value = "saku"
        },
      ],
      image     = "138046196805.dkr.ecr.ap-northeast-1.amazonaws.com/saku-coco-prep-saku-module-daas-consumer:latest"
      essential = true
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          awslogs-region        = "ap-northeast-1"
          awslogs-stream-prefix = "saku-module-daas-consumer-mysql"
          awslogs-group         = "saku-coco-prep-ecs-saku-module-daas-consumer-mysql"
        }
        secretOptions = []
      }
      name                   = "saku-module-daas-consumer-mysql"
      readonlyRootFilesystem = false
      secrets = [
        {
          name      = "DB_PASSWORD"
          valueFrom = "arn:aws:secretsmanager:ap-northeast-1:138046196805:secret:saku-prep-db20210706082649361400000001-OvgJh5:password::"
        },
        {
          name      = "DB_USERNAME"
          valueFrom = "arn:aws:secretsmanager:ap-northeast-1:138046196805:secret:saku-prep-db20210706082649361400000001-OvgJh5:username::"
        },
      ]
      systemControls = []
      mountPoints    = []
      volumesFrom    = []
      portMappings = [
        {
          containerPort = 8080
          hostPort      = 8080
          name          = "saku-module-daas-consumer-mysql-8080-tcp"
          protocol      = "tcp"
        }
      ]
      ulimits = [
        {
          name      = "nofile"
          softLimit = 10000
          hardLimit = 10000
        }
      ]
    }]

    stag = [{
      environment = [
        {
          name  = "BLOCK_HISTORY_ENV"
          value = "stag"
        },
        {
          name  = "DB_ENDPOINT"
          value = "saku-stag-db.cluster-ci4jygjfmibd.ap-northeast-1.rds.amazonaws.com"
        },
        {
          name  = "DB_ENGINE"
          value = "mysql"
        },
        {
          name  = "DB_NAME"
          value = "jcl"
        },
        {
          name  = "DB_TABLE_LIVETOVOD"
          value = "live_to_vod"
        },
        {
          name  = "DB_TABLE_PROGRAM_INFO"
          value = "program_info"
        },
        {
          name  = "DDB_LOCK_TABLE"
          value = "saku-coco-stag-saku-module-program-lock-mysql-consumer"
        },
        {
          name  = "ENV"
          value = "stag"
        },
        {
          name  = "ENVIRONMENT"
          value = "stag"
        },
        {
          name = "KAFKA_BROKER_URLS"
          value = jsonencode(
            [
              "b-1.daas-stag.kafka:9092",
              "b-2.daas-stag.kafka:9092",
              "b-3.daas-stag.kafka:9092",
            ]
          )
        },
        {
          name  = "KAFKA_CONSUMER_GROUP_ID"
          value = "coco-saku-module-daas-consumer-MySQL-stag"
        },
        {
          name  = "KAFKA_PROGRAM_INFO_TOPIC_NAME"
          value = "jcom-saku-stag-meta-notification"
        },
        {
          name  = "PARAMETERS_CHANNEL_CONVERT_NAME"
          value = "saku-coco-stag-saku-module-channel-convert"
        },
        {
          name  = "PARAMETERS_CHANNEL_ID_LIST_NAME"
          value = "saku-coco-stag-saku-module-channel-id"
        },
        {
          name  = "QUEUE_CATCH_UP_URL"
          value = "https://sqs.ap-northeast-1.amazonaws.com/138046196805/saku-stag-catchup"
        },
        {
          name  = "SENTRY_DSN"
          value = "https://<EMAIL>/5938405"
        },
        {
          name  = "SNS_ERROR_NOTIFICATION_NAME"
          value = "saku-coco-stag-error-notification"
        },
        {
          name  = "VENDOR_ID"
          value = ""
        },
        {
          name  = "VENDOR_TOKEN"
          value = "saku"
        },
      ],
      image     = "138046196805.dkr.ecr.ap-northeast-1.amazonaws.com/saku-coco-stag-saku-module-daas-consumer:latest"
      essential = true
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          awslogs-region        = "ap-northeast-1"
          awslogs-stream-prefix = "saku-module-daas-consumer-mysql"
          awslogs-group         = "saku-coco-stag-ecs-saku-module-daas-consumer-mysql"
        }
        secretOptions = []
      }
      name                   = "saku-module-daas-consumer-mysql"
      readonlyRootFilesystem = false
      secrets = [
        {
          name      = "DB_PASSWORD"
          valueFrom = "arn:aws:secretsmanager:ap-northeast-1:138046196805:secret:saku-stag-db20210706082441282600000001-kU17zU:password::"
        },
        {
          name      = "DB_USERNAME"
          valueFrom = "arn:aws:secretsmanager:ap-northeast-1:138046196805:secret:saku-stag-db20210706082441282600000001-kU17zU:username::"
        },
      ]
      systemControls = []
      mountPoints    = []
      volumesFrom    = []
      portMappings = [
        {
          containerPort = 8080
          hostPort      = 8080
          name          = "saku-module-daas-consumer-mysql-8080-tcp"
          protocol      = "tcp"
        }
      ]
      ulimits = [
        {
          name      = "nofile"
          softLimit = 10000
          hardLimit = 10000
        }
      ]
    }]
  }
  tags = {
    common = {
      Environment = "saku-coco-${terraform.workspace}"
      EnvName     = "saku-${terraform.workspace}"
    }
  }
}
