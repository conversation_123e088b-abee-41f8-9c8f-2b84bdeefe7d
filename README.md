## Project Overview

This project contains Terraform configurations specifically for the Saku project’s JCL, managing related AWS resources and applications within EKS. For those unfamiliar with JCL, it consists of three components: Jon, which is responsible for DRM; Coco, which handles live streaming; and Luke, which manages encoding.

## Directory Structure

The root directory is primarily organized by AWS resources, with each service structured as an independent Terraform module that can be applied separately. Dependencies between modules are handled using data blocks to establish relationships.

## Usage Examples

1. To modify the desired count of the EKS node group `luke`, update the corresponding `scaling_config` in `eks/main.tf`.
2. To add or modify subnet and route-related configurations, go to the `network` directory.
3. To modify the replica count of an application within EKS, update the corresponding Helm values in the eks_app directory. For example, to adjust the replica count for Playready in the prod environment, modify the replicaCount attribute in `eks_app/charts/jon/playready/values/prod.yaml`.
4. To install EKS addons, go to the `eks` directory and write the name and version of the addon to the `local.tf`
   ```YAML
    versions = {
      stag = {
        cluster = "1.31"
        addons = {
          vpc-cni                         = "v1.19.2-eksbuild.1"
          aws-ebs-csi-driver              = "v1.38.1-eksbuild.1"
          coredns                         = "v1.11.4-eksbuild.1"
          kube-proxy                      = "v1.31.2-eksbuild.3"
          amazon-cloudwatch-observability = "v3.6.0-eksbuild.2"
        }
      }
    }
   ```

Different environments are managed using Terraform workspaces. For example, to apply changes to the prod environment, first switch to the prod workspace using `terraform workspace select prod`, then run `terraform apply`.
