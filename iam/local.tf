locals {
  aws_region = "ap-northeast-1"
  env        = terraform.workspace == "stag" ? "stage" : terraform.workspace

  eks_cluster_oidc_provider = {
    prod = "oidc.eks.ap-northeast-1.amazonaws.com/id/6C23DB44986B53F74BB0E6536985F1A4"
    prep = "oidc.eks.ap-northeast-1.amazonaws.com/id/34EDD38C92651D646F997EA882672428"
    stag = "oidc.eks.ap-northeast-1.amazonaws.com/id/EEF78B478B346E257FE07D9DFC95165D"
  }
  tags = {
    common = {
      Environment = local.env
      Region      = local.aws_region
      Vendor      = "saku"
      EnvName     = "saku-${terraform.workspace}"
    }
  }
}
