resource "aws_iam_role" "saku_eks_master" {
  name        = "saku-${local.env}-eks-master"
  description = "EKS IAM Role for saku ${local.env} Cluster managed by Terraform"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Service = "eks.amazonaws.com"
        },
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = merge(local.tags.common, {
    App           = "eks"
    "aws.service" = "iam.role"
    "tf.module"   = "eks"
  })
}

resource "aws_iam_role" "saku_eks_node" {
  name        = "saku-${local.env}-eks-node"
  description = "EKS IAM Role for saku ${local.env} node managed by Terraform"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Service = "ec2.amazonaws.com"
        },
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = merge(local.tags.common, {
    App           = "eks.node"
    "aws.service" = "iam.role"
    "tf.module"   = "eks"
  })
}

resource "aws_iam_role" "saku_coco_execution_saku_module_daas_consumer_mysql" {
  name = "saku-coco-${terraform.workspace}-execution-saku-module-daas-consumer-mysql"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        },
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = {
    Environment = "saku-coco-${terraform.workspace}"
    Name        = "saku-coco-${terraform.workspace}-role-execution-saku-module-daas-consumer-mysql"
    EnvName     = "saku-${terraform.workspace}"
  }
}

resource "aws_iam_role" "saku_coco_service_saku_module_daas_consumer_mysql" {
  name = "saku-coco-${terraform.workspace}-service-saku-module-daas-consumer-mysql"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        },
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = {
    Environment = "saku-coco-${terraform.workspace}"
    Name        = "saku-coco-${terraform.workspace}-role-service-saku-module-daas-consumer-mysql"
    EnvName     = "saku-${terraform.workspace}"
  }
}

resource "aws_iam_role" "saku_coco_saku_program_info_checker" {
  count = contains(["prod", "stag"], terraform.workspace) ? 1 : 0
  name  = "saku-${local.env}-coco-saku-program-info-checker"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Service = "lambda.amazonaws.com"
        },
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = merge(local.tags.common, {
    Service = "infrastructure"
  })
}

resource "aws_iam_role" "saku_prod_coco_maintenance_catchup_programs_info_fetcher_backend" {
  count       = terraform.workspace == "prod" ? 1 : 0
  name        = "saku-prod-coco-chat-lambda-20221222021104857300000001"
  path        = "/saku/prod/coco/"
  description = "Role for saku prod Coco lambda, managed by Terraform"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Service = "lambda.amazonaws.com"
        },
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = merge(local.tags.common, {
    App           = "lambda"
    Service       = "coco"
    "aws.service" = "iam.role"
    "tf.module"   = "coco.main"
  })
}

# IAM user
resource "aws_iam_user" "app_s3_access" {
  count = terraform.workspace == "stag" ? 1 : 0
  name  = "saku-${terraform.workspace}-app"

  tags = local.tags.common
}

resource "aws_iam_user_policy" "app_s3_access" {
  count = terraform.workspace == "stag" ? 1 : 0
  name  = "saku-${terraform.workspace}-app-s3-access"
  user  = aws_iam_user.app_s3_access[0].name

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "s3:GetObject",
          "s3:DeleteObject",
          "s3:PutObject",
          "s3:ListBucket",
          "s3:AbortMultipartUpload"
        ],
        Resource = [
          "arn:aws:s3:::kks-${terraform.workspace}-saku-studio",
          "arn:aws:s3:::kks-${terraform.workspace}-saku-studio/*",
          "arn:aws:s3:::saku-${terraform.workspace}-shared-studio",
          "arn:aws:s3:::saku-${terraform.workspace}-shared-studio/*"
        ]
      },
      {
        Effect = "Allow",
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:ListBucket",
          "s3:AbortMultipartUpload"
        ],
        Resource = [
          "arn:aws:s3:::kks-${terraform.workspace}-saku-theater",
          "arn:aws:s3:::kks-${terraform.workspace}-saku-theater/*"
        ]
      },
      {
        Effect = "Allow",
        Action = [
          "s3:PutObject",
          "s3:ListBucket"
        ],
        Resource = [
          "arn:aws:s3:::kks-${terraform.workspace}-saku-live2vod-theater",
          "arn:aws:s3:::kks-${terraform.workspace}-saku-live2vod-theater/*",
        ]
      },
      {
        Effect = "Allow",
        Action = [
          "s3:GetObject",
          "s3:ListBucket"
        ],
        Resource = [
          "arn:aws:s3:::kks-${terraform.workspace}-saku-source-upload",
          "arn:aws:s3:::kks-${terraform.workspace}-saku-source-upload/*",
          "arn:aws:s3:::saku-${terraform.workspace}-telasa-shared-source-upload",
          "arn:aws:s3:::saku-${terraform.workspace}-telasa-shared-source-upload/*"
        ]
      }
    ]
  })
}

resource "aws_iam_user_policy" "app_swf_access" {
  count = terraform.workspace == "stag" ? 1 : 0
  name  = "saku-${terraform.workspace}-app-swf-access"
  user  = aws_iam_user.app_s3_access[0].name

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "swf:StartWorkflowExecution",
          "swf:RespondActivityTaskCompleted",
          "swf:RespondActivityTaskFailed",
          "swf:RecordActivityTaskHeartbeat",
          "swf:PollForDecisionTask",
          "swf:PollForActivityTask",
          "swf:RespondDecisionTaskCompleted",
          "swf:GetWorkflowExecutionHistory",
          "swf:DescribeWorkflowExecution",
          "swf:DescribeActivityType",
          "swf:DescribeWorkflowType",
          "swf:RegisterWorkflowType",
          "swf:RegisterActivityType",
          "swf:ListOpenWorkflowExecutions",
          "swf:ListClosedWorkflowExecutions",
          "swf:StartChildWorkflowExecution",
          "swf:CancelWorkflowExecution",
          "swf:ScheduleActivityTask",
          "swf:CompleteWorkflowExecution"
        ],
        Resource = [
          "arn:aws:swf:ap-northeast-1:138046196805:/domain/saku-${terraform.workspace}-encoding",
          "arn:aws:swf:ap-northeast-1:138046196805:/domain/saku-${terraform.workspace}-encoding-priority",
          "arn:aws:swf:ap-northeast-1:138046196805:/domain/saku-${terraform.workspace}-encoding-premium"
        ]
      }
    ]
  })
}

module "cloudwatch_observability_irsa_role" {
  count   = terraform.workspace == "stag" ? 1 : 0
  source  = "terraform-aws-modules/iam/aws//modules/iam-assumable-role-with-oidc"
  version = "~> 5.0"

  create_role                   = true
  role_name                     = "cloudwatch-observability-saku-${terraform.workspace == "stag" ? "stage" : terraform.workspace}"
  provider_url                  = local.eks_cluster_oidc_provider[terraform.workspace]
  role_policy_arns              = ["arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"]
  oidc_fully_qualified_subjects = ["system:serviceaccount:amazon-cloudwatch:cloudwatch-agent"]
}
