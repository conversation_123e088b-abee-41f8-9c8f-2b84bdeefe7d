# variables
# PROJECT_NAME

include:
  - project: kkstream/engineeringproductivity/cicd
    ref: master
    file:
      - cicd_backend/common-terraform.yml
  - project: kkstream/jcl/jcl-infrastructure/cicd-config
    ref: main
    file:
      - infra/terraform-jobs-config.yml

.setup_git: &setup_git
  - git config --global url."https://kks-all-star:${GITLAB_ACCESS_TOKEN}@gitlab.kkinternal.com".insteadOf https://gitlab.kkinternal.com

.setup_env_by_branch: &setup_env_by_branch
  - |
    if [ -n "${CI_COMMIT_BRANCH}" ]; then
      IFS='#' read -r TERRAFORM_PATH ENV <<< "${CI_COMMIT_BRANCH}"
    else
      IFS='#' read -r TERRAFORM_PATH ENV <<< "${target}"
    fi
  - echo "TERRAFORM_PATH:$TERRAFORM_PATH"
  - echo "ENV:$ENV"

.check_env: &check_env
  - |
    if [ -z "$TERRAFORM_PATH" ]; then
      echo "ERROR: TERRAFORM_PATH variable not set. Please set the TERRAFORM_PATH variable."
      exit 1
    fi
    if [ -z "$ENV" ]; then
      echo "ERROR: ENV variable not set. Please set the ENV variable."
      exit 1
    fi

.setup_aws_role: &setup_aws_role
  - export TF_VAR_infra_role_external_id=${PROD_EXTERNAL_ID}
  - unset AWS_ACCESS_KEY_ID
  - unset AWS_SECRET_ACCESS_KEY
  - unset AWS_DEFAULT_REGION
  - aws configure set role_session_name session-assume-role-by-ep-cd
  - aws configure set credential_source Ec2InstanceMetadata
  - aws configure set role_arn ${PROD_ROLE_ARN}
  - aws configure set external_id ${PROD_EXTERNAL_ID}
  - aws sts get-caller-identity

plan:
  image:
    name: $CIAAS_IMAGE_REGISTRY/kks/cd-tools/aws-py3:k8s-1.30.8-bundle
  before_script:
    - *setup_git
    - *setup_env_by_branch
    - *check_env
    - *setup_aws_role

run:
  image:
    name: $CIAAS_IMAGE_REGISTRY/kks/cd-tools/aws-py3:k8s-1.30.8-bundle
  before_script:
    - *setup_git
    - *setup_env_by_branch
    - *check_env
    - *setup_aws_role

version_tagging:
  image:
    name: $CIAAS_IMAGE_REGISTRY/kks/semantic-release:19.0.3
  script:
    - git clone --depth 1 --single-branch https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.kkinternal.com/${CI_PROJECT_NAMESPACE}/cicd-config.git
    - cp cicd-config/infra/.releaserc.json .releaserc.json
    - export GL_TOKEN=$GITLAB_ACCESS_TOKEN
    - semantic-release