resource "aws_lambda_function" "saku_coco_saku_program_info_checker" {
  count         = contains(["prod", "stag"], terraform.workspace) ? 1 : 0
  function_name = "saku-${local.env}-coco-saku-program-info-checker"
  role          = data.aws_iam_role.saku_coco_saku_program_info_checker.arn
  package_type  = "Image"
  image_uri     = "138046196805.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-saku-coco-program-info-checker:${local.coco-saku-program-info-checker[terraform.workspace].image_uri_version}"
  image_config {
    command = [
      "main.lambda_handler"
    ]
    entry_point = [
      "python",
      "-m",
      "awslambdaric"
    ]
    working_directory = "/app"
  }

  timeout     = 180
  memory_size = 1024

  vpc_config {
    ipv6_allowed_for_dual_stack = false
    security_group_ids          = local.coco-saku-program-info-checker[terraform.workspace].security_group_ids
    subnet_ids                  = local.coco-saku-program-info-checker[terraform.workspace].subnet_ids
  }

  environment {
    variables = local.coco-saku-program-info-checker[terraform.workspace].environment.variables
  }
  tags = merge(
    local.tags.common,
    {
      Service               = "infrastructure"
      terraform-aws-modules = "lambda"
    }
  )
}

# lambda for saku-prod-coco-maintenance-catchup-programs-info-fetcher-backend
resource "aws_lambda_function" "saku_prod_coco_maintenance_catchup_programs_info_fetcher_backend" {
  count         = terraform.workspace == "prod" ? 1 : 0
  function_name = "saku-prod-coco-maintenance-catchup-programs-info-fetcher-backend"
  role          = data.aws_iam_role.saku_prod_coco_maintenance_catchup_programs_info_fetcher_backend.arn
  package_type  = "Image"
  image_uri     = "138046196805.dkr.ecr.ap-northeast-1.amazonaws.com/jcl-coco-catchup-program-list-fetcher:v1.5.0"
  image_config {
    command = [
      "backend"
    ]
    entry_point = []
  }

  timeout     = 210
  memory_size = 128

  vpc_config {
    ipv6_allowed_for_dual_stack = false
    security_group_ids = [
      "sg-0250491e4aedcf211"
    ]
    subnet_ids = [
      "subnet-0ad1c155103b7cdae",
      "subnet-0b0e1537a35e35ef0",
      "subnet-0d699d9a29137a58d"
    ]
  }


  tags = merge(
    local.tags.common,
    {
      Name          = "saku-prod-coco-maintenance-catchup-programs-info-fetcher-backend"
      App           = "maintenance-catchup-programs-info-fetcher.backend"
      Service       = "coco"
      "aws.service" = "lambda.function"
    }
  )
}
