locals {
  aws_region = "ap-northeast-1"
  env        = terraform.workspace == "stag" ? "stage" : terraform.workspace

  coco-saku-program-info-checker = {
    prod = {
      image_uri_version = "1.24.09263"
      environment = {
        variables = {
          DAAS_API_SECRET_NAME         = "saku-prod-dass-api-info"
          DB_ENDPOINT                  = "saku-prod-db.cluster-ro-ci4jygjfmibd.ap-northeast-1.rds.amazonaws.com"
          DB_NAME                      = "jcl"
          DB_SECRET_NAME               = "saku-prod-db20210706082713817800000001"
          SKM_CHANNEL_CONVERT_SSM_NAME = "saku-coco-prod-saku-module-channel-convert"
          SKM_CHANNEL_ID_SSM_NAME      = "saku-coco-prod-saku-module-channel-id"
          URL_SLACK_WEBHOOK            = "*******************************************************************************"
        }
      }

      security_group_ids = [
        "sg-0bab6c154c9903013"
      ]

      subnet_ids = [
        "subnet-079cc61a5b6b3511b",
        "subnet-088ab4f384b215c8d",
      ]
    }

    stag = {
      image_uri_version = "1.24.0926"
      environment = {
        variables = {
          DB_ENDPOINT                  = "saku-stag-db.cluster-ro-ci4jygjfmibd.ap-northeast-1.rds.amazonaws.com"
          DB_SECRET_NAME               = "saku-stag-db20210706082441282600000001"
          DAAS_API_SECRET_NAME         = "saku-prod-dass-api-info"
          DB_NAME                      = "jcl"
          SKM_CHANNEL_CONVERT_SSM_NAME = "saku-coco-prod-saku-module-channel-convert"
          SKM_CHANNEL_ID_SSM_NAME      = "saku-coco-prod-saku-module-channel-id"
        }
      }
      security_group_ids = [
        "sg-05c66f5ed4a7a2ddb"
      ]

      subnet_ids = [
        "subnet-0f00ddcff844b59a8",
        "subnet-0fb9616cee592a681",
      ]
    }

  }

  tags = {
    common = {
      Environment = local.env
      Region      = local.aws_region
      Vendor      = "saku"
      EnvName     = "saku-${terraform.workspace}"
    }
  }
}
