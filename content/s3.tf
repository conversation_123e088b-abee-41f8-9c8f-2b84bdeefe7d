locals {
  env_name                = "kks-${var.env}"
  enable_share_content    = var.enable_share_content
  enable_s3_life_cycle    = var.enable_s3_life_cycle
  get_upload_source_roles = concat([var.ads_iam_arn], [var.daas_file_adaptor.role_arn], var.telasa_access_role_arn, var.saku_access_role_arn)
}

module "s3_raw_unified" {
  #checkov:skip=CKV_TF_1: "Ensure Terraform module sources use a commit hash"
  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "4.1.2"

  create_bucket = true
  bucket        = "${var.env}-unified-source-upload"

  attach_policy = true
  policy        = data.aws_iam_policy_document.s3_raw_unified.json

  control_object_ownership = true
}

data "aws_iam_policy_document" "s3_raw_unified" {
  policy_id = "ManagedByJCLTerraform"
  statement {
    sid    = "AllowGetPermissionFromOtherAccounts"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = local.get_upload_source_roles
    }

    actions = [
      "s3:ListBucket",
      "s3:GetObject",
      "s3:GetObjectTagging",
      "s3:GetObjectVersion"
    ]
    resources = [
      "${module.s3_raw_unified.s3_bucket_arn}/*",
      module.s3_raw_unified.s3_bucket_arn
    ]
  }
}

resource "aws_s3_bucket_notification" "s3_raw_unified" {
  count  = local.enable_share_content ? 1 : 0
  bucket = module.s3_raw_unified.s3_bucket_id
  topic {
    events = [
      "s3:ObjectCreated:*",
    ]
    topic_arn = aws_sns_topic.s3_source_upload.arn
  }

  depends_on = [aws_sns_topic_policy.s3_source_upload]
}

resource "aws_s3_bucket_lifecycle_configuration" "s3_raw_unified" {
  count  = local.enable_s3_life_cycle ? 1 : 0
  bucket = module.s3_raw_unified.s3_bucket_id

  rule {
    id     = "${module.s3_raw_unified.s3_bucket_id}-lifecycle-after30days"
    status = "Enabled"

    expiration {
      days                         = 30
      expired_object_delete_marker = false
    }
    noncurrent_version_expiration {
      noncurrent_days = 7
    }
    filter {
    }
  }

  rule {
    id     = "Clean-delete-marker"
    status = "Enabled"

    abort_incomplete_multipart_upload {
      days_after_initiation = 30
    }
    expiration {
      days                         = 0
      expired_object_delete_marker = true
    }
    filter {
    }
  }
}

module "s3_studio_shared" {
  #checkov:skip=CKV_TF_1: "Ensure Terraform module sources use a commit hash"
  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "4.1.2"

  create_bucket = true

  bucket = "${local.env_name}-shared-studio"

  control_object_ownership = true
}

resource "aws_s3_bucket_lifecycle_configuration" "s3_studio_shared" {
  count  = local.enable_s3_life_cycle ? 1 : 0
  bucket = module.s3_studio_shared.s3_bucket_id

  rule {
    id     = "move-to-glacier-deep-archive-after-90days"
    status = "Enabled"

    transition {
      days          = 90
      storage_class = "DEEP_ARCHIVE"
    }
  }
}
