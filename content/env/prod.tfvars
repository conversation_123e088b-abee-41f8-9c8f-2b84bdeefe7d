env                  = "prod"
enable_share_content = true
enable_s3_life_cycle = true

daas_account_id   = "************" # bifrost
telasa_account_id = "************" # telasa
saku_account_id   = "************" # saku

telasa_access_role_arn = [
  "arn:aws:iam::************:role/ec2-videopass-production-worker",
  "arn:aws:iam::************:role/telasa-prod-service-encoding-transfer-service",
  "arn:aws:iam::************:role/telasa-prod-encoding-transfer-daily-retry-task-role"
]

saku_access_role_arn = [
  "arn:aws:iam::************:role/saku-prod-service-landing_service"
]

daas_file_adaptor = {
  sqs_arn  = "arn:aws:sqs:ap-northeast-1:************:daas-prod-sqs-file-adaptor"
  role_arn = "arn:aws:iam::************:role/daas-prod-file-adaptor"
}