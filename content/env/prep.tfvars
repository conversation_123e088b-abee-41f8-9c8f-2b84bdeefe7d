env                  = "prep"
enable_share_content = true
enable_s3_life_cycle = true

daas_account_id   = "************" # bifrost
telasa_account_id = "************" # telasa
saku_account_id   = "************" # saku

telasa_access_role_arn = [
  "arn:aws:iam::************:role/ec2-videopass-staging-worker",
  "arn:aws:iam::************:role/telasa-stage-service-encoding-transfer-service",
  "arn:aws:iam::************:role/telasa-stage-encoding-transfer-daily-retry-task-role"
]

saku_access_role_arn = [
  "arn:aws:iam::************:role/saku-prep-service-landing_service"
]

daas_file_adaptor = {
  sqs_arn  = "arn:aws:sqs:ap-northeast-1:************:daas-beta-sqs-file-adaptor"
  role_arn = "arn:aws:iam::************:role/daas-beta-file-adaptor"
}