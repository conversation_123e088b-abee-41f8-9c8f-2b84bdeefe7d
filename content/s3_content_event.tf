resource "aws_sns_topic" "s3_source_upload" {
  name = "${local.env_name}-s3-source-upload"
}

resource "aws_sns_topic_policy" "s3_source_upload" {
  arn    = aws_sns_topic.s3_source_upload.arn
  policy = data.aws_iam_policy_document.sns_source_upload.json
}

data "aws_iam_policy_document" "sns_source_upload" {
  statement {
    effect = "Allow"
    principals {
      type        = "Service"
      identifiers = ["s3.amazonaws.com"]
    }
    actions   = ["sns:Publish"]
    resources = [aws_sns_topic.s3_source_upload.arn]
    condition {
      test     = "ArnLike"
      variable = "aws:SourceArn"
      values   = [module.s3_raw_unified.s3_bucket_arn]
    }
  }

  statement {
    sid    = "AllowContentSharingSNSPermission"
    effect = "Allow"
    principals {
      type = "AWS"
      identifiers = [
        var.telasa_account_id,
        var.saku_account_id,
        var.daas_account_id
      ]
    }
    actions = [
      "sns:Subscribe"
    ]
    resources = [aws_sns_topic.s3_source_upload.arn]
  }
}