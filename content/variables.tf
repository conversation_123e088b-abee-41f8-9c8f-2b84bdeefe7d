variable "env" {
  type = string
}

variable "enable_share_content" {
  type    = bool
  default = false
}

variable "enable_s3_life_cycle" {
  type    = bool
  default = false
}

variable "ads_iam_arn" {
  type        = string
  default     = "arn:aws:iam::************:root"
  description = "Allow get permission on source upload buckets."
}

variable "telasa_access_role_arn" {
  type    = list(string)
  default = []
}

variable "saku_access_role_arn" {
  type    = list(string)
  default = []
}

variable "daas_account_id" {
  type = string
}

variable "telasa_account_id" {
  type = string
}

variable "saku_account_id" {
  type = string
}

variable "daas_file_adaptor" {
  type = object({
    sqs_arn  = string
    role_arn = string
  })
}