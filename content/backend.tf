terraform {
  required_version = ">= 1.9.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 5.27.0"
    }
  }

  backend "s3" {
    bucket       = "saku-jcl-terraform-state"
    key          = "content/terraform.tfstate"
    region       = "ap-northeast-1"
    use_lockfile = true
  }
}

provider "aws" {
  region = "ap-northeast-1"

  default_tags {
    tags = {
      Environment = terraform.workspace
      EnvName     = "saku-jcl-${terraform.workspace}"
      Project     = "saku-jcl"
      Managed     = "Terraform"
      ManagedBy   = "saku-jcl-terraform/content"
    }
  }
}
