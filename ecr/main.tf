resource "aws_ecr_repository" "repositories" {
  for_each = terraform.workspace == "prod" ? local.ecr_repositories : {}
  name     = each.value

  image_scanning_configuration {
    scan_on_push = true
  }

  tags = merge(
    local.tags.common,
    strcontains(each.value, "jon") ? {
      "Service" = "jon"
      } : strcontains(each.value, "coco") ? {
      "Service" = "coco"
      } : strcontains(each.value, "luke") ? {
      "Service" = "luke"
    } : {},
  )
}

resource "aws_ecr_repository" "jcl_infra_service_prom_exporter" {
  count = terraform.workspace == "prod" ? 1 : 0
  name  = "jcl-infra-service-prom-exporter"
}

resource "aws_ecr_repository" "jcl_saku_billing_automatic_updater" {
  count = terraform.workspace == "prod" ? 1 : 0
  name  = "jcl-saku-billing-automatic-updater"
  tags = {
    EnvName = "saku-${terraform.workspace}"
  }
}

resource "aws_ecr_repository" "jcl_saku_coco_program_info_checker" {
  count = terraform.workspace == "prod" ? 1 : 0
  name  = "jcl-saku-coco-program-info-checker"

  image_scanning_configuration {
    scan_on_push = true
  }

  tags = merge(
    local.tags.common,
    {
      "Service" = "coco", EnvName = "saku-${terraform.workspace}"
    }
  )
}

resource "aws_ecr_repository" "saku_coco_module_daas_consumer" {
  name = "saku-coco-${terraform.workspace}-saku-module-daas-consumer"

  tags = {
    Environment = "saku-coco-${terraform.workspace}"
    Name        = "saku-coco-${terraform.workspace}-ecr-saku-module-daas-consumer"
    EnvName     = "saku-${terraform.workspace}"
  }
}

resource "aws_ecr_repository" "saku_operation_slack_bot" {
  count = terraform.workspace == "prod" ? 1 : 0
  name  = "saku-operation-slack-bot"

  image_scanning_configuration {
    scan_on_push = true
  }

  tags = merge(
    local.tags.common,
    {
      "Service" = "luke", EnvName = "saku-${terraform.workspace}"
    }
  )
}
