locals {
  aws_region = "ap-northeast-1"
  env        = terraform.workspace == "stag" ? "stage" : terraform.workspace

  tags = {
    common = {
      Region        = local.aws_region
      Usage         = "Container"
      "aws.service" = "ecr"
    }
  }

  ecr_repositories = {
    jcl_jon_drm_portal                  = "jcl-jon-drm-portal"
    jcl_jon_monitor                     = "jcl-jon-monitor"
    jcl_jon_key_server                  = "jcl-jon-key-server"
    jcl_jon_playready_server            = "jcl-jon-playready-server"
    jcl_jon_aws_speke                   = "jcl-jon-aws-speke"
    jcl_coco_catchup_scheduler          = "jcl-coco-catchup-scheduler"
    jcl_coco_internal_api               = "jcl-coco-internal-api"
    jcl_coco_linear_automatic_switch    = "jcl-coco-linear-automatic-switch"
    jcl_coco_manager                    = "jcl-coco-manager"
    jcl_coco_live_to_vod_status_checker = "jcl-coco-live-to-vod-status-checker"
    jcl_coco_portal                     = "jcl-coco-portal"
    jcl_luke_decider                    = "jcl-luke-decider"
    jcl_luke_internal_api               = "jcl-luke-internal-api"
    jcl_luke_job_manager                = "jcl-luke-job-manager"
    jcl_luke_public_api                 = "jcl-luke-public-api"
  }
}
