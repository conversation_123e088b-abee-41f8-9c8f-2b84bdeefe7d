resource "aws_iam_role" "runner_infra" {
  name               = "${var.project}-runner-infra"
  assume_role_policy = data.aws_iam_policy_document.runner_infra.json

  tags = {
    Environment = var.project
    Name        = "${var.project}-runner-iam-infra"
  }
}

data "aws_iam_policy_document" "runner_infra" {
  statement {
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::190446795153:role/kks-jcl-common"]
    }
    condition {
      test     = "StringEquals"
      values   = [var.infra_role_external_id]
      variable = "sts:ExternalId"
    }
    actions = ["sts:AssumeRole"]
  }
}

resource "aws_iam_role_policy_attachment" "infra_policy_attachment" {
  policy_arn = aws_iam_policy.infra_policy.arn
  role       = aws_iam_role.runner_infra.id
}

resource "aws_iam_policy" "infra_policy" {
  name   = "${var.project}-runner-infra-role-policy"
  policy = data.aws_iam_policy_document.infra_policy.json
}

data "aws_iam_policy_document" "infra_policy" {
  statement {
    sid    = "AllowAssumeRole"
    effect = "Allow"
    actions = [
      "iam:GetRole",
      "iam:PassRole"
    ]
    resources = [
      "arn:aws:iam::${var.aws_account_id}:role/*"
    ]
  }
  statement {
    sid    = "AllowDescribeResource"
    effect = "Allow"
    actions = [
      # EC2
      "ec2:Describe*",
      # ECS
      "ecs:Describe*",
      "ecs:List*",
      # EKS
      "eks:Describe*",
      "eks:List*",
      "eks:DeleteNodegroup",
      "eks:UpdateNodegroupConfig",
      # ECR
      "ecr:Describe*",
      "ecr:List*",
      # S3
      "s3:List*",
      "s3:Get*",
      # IAM
      "iam:Get*",
      "iam:List*",
      "iam:TagRole",
      "iam:TagUser",
      "iam:PutUserPolicy",
      # CloudWatch
      "cloudwatch:Describe*",
      "logs:Describe*",
      # Route53
      "route53:List*",
      # SSM
      "ssm:Describe*",
      "ssm:Get*",
      # Secrets Manager
      "secretsmanager:List*",
      "secretsmanager:Describe*",
      # Lambda
      "lambda:Get*",
      "lambda:List*",
      "lambda:Update*",
      "lambda:Invoke*",
      # SNS
      "sns:List*",
      "sns:Get*"
    ]
    resources = ["*"]
  }

  statement {
    sid    = "AllowCreateResource"
    effect = "Allow"
    actions = [
      # EC2
      "ec2:Create*",
      # ECS
      "ecs:Create*",
      "ecs:*Tag*",
      # EKS
      "eks:Create*",
      "eks:*Tag*",
      # ECR
      "ecr:Create*",
      "ecr:*Tag*",
      # S3
      "s3:Create*",
      "s3:Put*",
      "s3:*Tag*",
      # CloudWatch
      "cloudwatch:Put*",
      "logs:Create*",
      "logs:*Tag*",
      "logs:Put*",
      # Route53
      "route53:Create*",
      "route53:Change*",
      # SSM
      "ssm:Put*",
      "ssm:*Tag*",
      # Secrets Manager
      "secretsmanager:Create*",
      "secretsmanager:*Tag*",
      "secretsmanager:Update*",
      # Lambda
      "lambda:Create*",
      "lambda:*Tag*",
      "lambda:Publish*",
      # SNS
      "sns:Create*",
      "sns:*Tag*",
      "sns:Set*",
      "tag:*"
    ]
    resources = ["*"]
  }

  statement {
    sid    = "AllowModifyAllResourceInThisTerraform"
    effect = "Allow"
    actions = [
      "ec2:*",
      "ecs:*",
      "eks:*",
      "ecr:*",
      "s3:*",
      "cloudwatch:*",
      "logs:*",
      "route53:*",
      "ssm:*",
      "secretsmanager:*",
      "rds:*",
      "lambda:*",
      "sns:*"
    ]
    resources = ["*"]
    condition {
      test     = "StringLike"
      variable = "aws:ResourceTag/ManagedBy"
      values   = ["saku-jcl-terraform*"]
    }
    condition {
      test     = "StringLike"
      variable = "aws:RequestTag/ManagedBy"
      values   = ["saku-jcl-terraform*"]
    }
  }
  statement {
    sid    = "AllowDeletePermission"
    effect = "Allow"
    actions = [
      "s3:Delete*",
      "ssm:Delete*",
      "secretsmanager:Delete*",
      "lambda:Delete*",
    ]
    resources = [
      "arn:aws:s3:::saku-jcl-terraform-state*",
      "arn:aws:s3:::*-shared-studio",
      "arn:aws:s3:::*-unified-source-upload"
    ]
  }
  statement {
    sid    = "DenyGetOwnPolicy"
    effect = "Deny"
    actions = [
      "iam:Get*",
      "iam:List*"
    ]
    resources = [
      aws_iam_role.runner_infra.arn
    ]
  }
}
